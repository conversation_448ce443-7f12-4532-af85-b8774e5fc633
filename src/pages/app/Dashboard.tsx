import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import AppShell from '@/components/layout/AppShell';
import PageHeader from '@/components/layout/PageHeader';
import {
  BarChart3,
  FileText,
  Receipt,
  MessageSquare,
  Mail,
  Users,
  TrendingUp,
  DollarSign,
  Plus,
  ArrowUpRight,
  ArrowDownRight,
  Clock,
  CheckCircle,
  AlertCircle,
  Calendar
} from 'lucide-react';

const Dashboard: React.FC = () => {
  const { userProfile, currentOrganization, userRole } = useAuth();

  const stats = [
    {
      title: 'חשבוניות החודש',
      value: '24',
      change: '+12%',
      changeType: 'positive' as const,
      icon: FileText,
      description: 'מהחודש הקודם',
      trend: 'עלייה',
    },
    {
      title: 'הכנסות החודש',
      value: '₪45,280',
      change: '+8%',
      changeType: 'positive' as const,
      icon: DollarSign,
      description: 'מהחודש הקודם',
      trend: 'עלייה',
    },
    {
      title: 'הוצאות החודש',
      value: '₪18,450',
      change: '-5%',
      changeType: 'negative' as const,
      icon: Receipt,
      description: 'מהחודש הקודם',
      trend: 'ירידה',
    },
    {
      title: 'לקוחות פעילים',
      value: '47',
      change: '+15%',
      changeType: 'positive' as const,
      icon: Users,
      description: 'מהחודש הקודם',
      trend: 'עלייה',
    },
  ];

  const quickActions = [
    {
      title: 'חשבונית מס',
      description: 'צור חשבונית מס חדשה',
      icon: FileText,
      href: '/app/documents/new?type=tax-invoice',
    },
    {
      title: 'חשבונית מס-קבלה',
      description: 'צור חשבונית מס-קבלה',
      icon: Receipt,
      href: '/app/documents/new?type=invoice-receipt',
    },
    {
      title: 'הוצאה חדשה',
      description: 'הוסף הוצאה חדשה',
      icon: Plus,
      href: '/app/expenses/new',
    },
    {
      title: 'דוח מהיר',
      description: 'צור דוח מותאם אישית',
      icon: BarChart3,
      href: '/app/reports',
    },
  ];

  const recentActivity = [
    {
      id: 1,
      type: 'invoice',
      title: 'חשבונית מס #2024-001',
      description: 'נוצרה עבור לקוח ABC בע"מ',
      time: 'לפני 2 שעות',
      status: 'pending',
      amount: '₪2,500',
    },
    {
      id: 2,
      type: 'expense',
      title: 'הוצאה אושרה',
      description: 'דלק לרכב החברה',
      time: 'לפני 4 שעות',
      status: 'approved',
      amount: '₪180',
    },
    {
      id: 3,
      type: 'payment',
      title: 'תשלום התקבל',
      description: 'חשבונית #2024-045',
      time: 'לפני יום',
      status: 'completed',
      amount: '₪5,200',
    },
  ];

  const upcomingTasks = [
    {
      id: 1,
      title: 'דוח מע"מ לחודש נובמבר',
      dueDate: '15/12/2024',
      priority: 'high',
      type: 'tax-report',
    },
    {
      id: 2,
      title: 'אישור הוצאות ממתינות',
      dueDate: 'היום',
      priority: 'medium',
      type: 'approval',
    },
    {
      id: 3,
      title: 'עדכון פרטי לקוח XYZ',
      dueDate: 'מחר',
      priority: 'low',
      type: 'client-update',
    },
  ];

  return (
    <AppShell>
      <div className="space-y-8">
        {/* Page Header */}
        <PageHeader
          title={`שלום, ${userProfile?.first_name || 'משתמש'}!`}
          subtitle={`ברוך הבא ל${currentOrganization?.name} • ${userRole}`}
          actions={
            <div className="flex items-center gap-3">
              <Button variant="outline" className="gap-2">
                <TrendingUp className="h-4 w-4" />
                דוח מהיר
              </Button>
              <Button className="gap-2">
                <Plus className="h-4 w-4" />
                חשבונית חדשה
              </Button>
            </div>
          }
        />

        {/* Stats Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat) => (
            <Card key={stat.title} className="cosmic-card hover:cosmic-glow transition-all duration-300">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {stat.title}
                </CardTitle>
                <div className="p-2 rounded-lg bg-muted">
                  <stat.icon className="h-4 w-4 text-foreground" />
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-3xl font-bold text-foreground">{stat.value}</div>
                <div className="flex items-center gap-2">
                  {stat.changeType === 'positive' ? (
                    <ArrowUpRight className="h-4 w-4 text-foreground" />
                  ) : (
                    <ArrowDownRight className="h-4 w-4 text-muted-foreground" />
                  )}
                  <span className={`text-sm font-medium ${
                    stat.changeType === 'positive' ? 'text-foreground' : 'text-muted-foreground'
                  }`}>
                    {stat.change}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {stat.description}
                  </span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-foreground">פעולות מהירות</h2>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {quickActions.map((action) => (
              <Card
                key={action.title}
                className="cosmic-card cursor-pointer hover:cosmic-glow transition-all duration-300 group"
              >
                <CardHeader className="pb-4">
                  <div className="w-12 h-12 rounded-xl bg-muted flex items-center justify-center mb-3 group-hover:scale-110 transition-transform group-hover:bg-foreground/10">
                    <action.icon className="h-6 w-6 text-foreground group-hover:text-foreground" />
                  </div>
                  <CardTitle className="text-lg font-semibold">{action.title}</CardTitle>
                  <CardDescription className="text-sm">{action.description}</CardDescription>
                </CardHeader>
              </Card>
            ))}
          </div>
        </div>

        {/* Recent Activity & Tasks */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Recent Activity */}
          <Card className="cosmic-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                פעילות אחרונה
              </CardTitle>
              <CardDescription>
                הפעולות האחרונות שבוצעו במערכת
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start gap-4 p-3 rounded-lg hover:bg-accent/50 transition-colors">
                    <div className={`w-2 h-2 rounded-full mt-2 ${
                      activity.status === 'completed' ? 'bg-foreground' :
                      activity.status === 'pending' ? 'bg-muted-foreground' :
                      activity.status === 'approved' ? 'bg-foreground' : 'bg-muted-foreground'
                    }`}></div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-foreground truncate">
                          {activity.title}
                        </p>
                        <span className="text-sm font-medium text-foreground">
                          {activity.amount}
                        </span>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {activity.description}
                      </p>
                      <p className="text-xs text-muted-foreground mt-1">
                        {activity.time}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Upcoming Tasks */}
          <Card className="cosmic-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                משימות קרובות
              </CardTitle>
              <CardDescription>
                משימות שדורשות תשומת לב
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {upcomingTasks.map((task) => (
                  <div key={task.id} className="flex items-start gap-4 p-3 rounded-lg hover:bg-accent/50 transition-colors">
                    <div className="flex items-center gap-2">
                      {task.priority === 'high' ? (
                        <AlertCircle className="h-4 w-4 text-foreground" />
                      ) : task.priority === 'medium' ? (
                        <Clock className="h-4 w-4 text-foreground" />
                      ) : (
                        <CheckCircle className="h-4 w-4 text-muted-foreground" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-foreground truncate">
                          {task.title}
                        </p>
                        <Badge variant={
                          task.priority === 'high' ? 'secondary' :
                          task.priority === 'medium' ? 'outline' : 'outline'
                        } className={task.priority === 'medium' ? 'border-foreground/30 text-foreground' : ''}>
                          {task.dueDate}
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {task.type === 'tax-report' ? 'דוח מס' :
                         task.type === 'approval' ? 'אישור' :
                         task.type === 'client-update' ? 'עדכון לקוח' : task.type}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppShell>
  );
};

export default Dashboard;
