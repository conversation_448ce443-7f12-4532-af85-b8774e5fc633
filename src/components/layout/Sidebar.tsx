import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import Logo from '@/components/Logo';
import {
  LayoutDashboard,
  FileText,
  Users,
  Receipt,
  BarChart3,
  Settings,
  Plus,
  LogOut,
  Building2
} from 'lucide-react';

interface SidebarProps {
  onNavigate?: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ onNavigate }) => {
  const location = useLocation();
  const { signOut, currentOrganization, userProfile } = useAuth();

  const navigation = [
    {
      name: 'לוח בקרה',
      href: '/dashboard',
      icon: LayoutDashboard,
      current: location.pathname === '/dashboard',
    },
    {
      name: 'מסמכים',
      href: '/app/documents',
      icon: FileText,
      current: location.pathname.startsWith('/app/documents'),
    },
    {
      name: 'לקוחות',
      href: '/app/clients',
      icon: Users,
      current: location.pathname.startsWith('/app/clients'),
    },
    {
      name: 'הוצאות',
      href: '/app/expenses',
      icon: Receipt,
      current: location.pathname.startsWith('/app/expenses'),
    },
    {
      name: 'דוחות',
      href: '/app/reports',
      icon: BarChart3,
      current: location.pathname.startsWith('/app/reports'),
    },
    {
      name: 'הגדרות',
      href: '/app/settings',
      icon: Settings,
      current: location.pathname.startsWith('/app/settings'),
    },
  ];

  const quickActions = [
    {
      name: 'חשבונית חדשה',
      href: '/app/documents/new?type=invoice',
      icon: Plus,
    },
    {
      name: 'הוצאה חדשה',
      href: '/app/expenses/new',
      icon: Plus,
    },
  ];

  const handleNavigation = () => {
    if (onNavigate) {
      onNavigate();
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <div className="flex h-full flex-col cosmic-glass border-l border-border">
      {/* Logo and Organization */}
      <div className="flex h-16 shrink-0 items-center px-6 border-b border-border">
        <Logo />
      </div>

      {/* Organization Info */}
      <div className="px-6 py-4 border-b border-border">
        <div className="flex items-center gap-3">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
            <Building2 className="h-4 w-4" />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-foreground truncate">
              {currentOrganization?.name || 'ארגון'}
            </p>
            <p className="text-xs text-muted-foreground truncate">
              {userProfile?.first_name} {userProfile?.last_name}
            </p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-4 space-y-1">
        <div className="space-y-1">
          {navigation.map((item) => (
            <Link
              key={item.name}
              to={item.href}
              onClick={handleNavigation}
              className={cn(
                'group flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors',
                item.current
                  ? 'bg-accent text-accent-foreground'
                  : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
              )}
            >
              <item.icon className="h-4 w-4 shrink-0" />
              {item.name}
            </Link>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="pt-6">
          <p className="px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
            פעולות מהירות
          </p>
          <div className="mt-2 space-y-1">
            {quickActions.map((action) => (
              <Link
                key={action.name}
                to={action.href}
                onClick={handleNavigation}
                className="group flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium text-muted-foreground hover:bg-accent hover:text-accent-foreground transition-colors"
              >
                <action.icon className="h-4 w-4 shrink-0" />
                {action.name}
              </Link>
            ))}
          </div>
        </div>
      </nav>

      {/* User Actions */}
      <div className="p-4 border-t border-border">
        <Button
          variant="ghost"
          className="w-full justify-start gap-3 text-muted-foreground hover:text-foreground"
          onClick={handleSignOut}
        >
          <LogOut className="h-4 w-4" />
          התנתקות
        </Button>
      </div>
    </div>
  );
};

export default Sidebar;
