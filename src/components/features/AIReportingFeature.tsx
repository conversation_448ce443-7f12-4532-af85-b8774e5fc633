import React, { useState, useEffect } from 'react';

const AIReportingFeature: React.FC = () => {
  const [currentAlert, setCurrentAlert] = useState(0);
  const [showChart, setShowChart] = useState(false);

  const alerts = [
    {
      type: 'warning',
      title: 'עלייה בהוצאות',
      message: 'הוצאות החודש עלו ב-15% לעומת החודש הקודם',
      icon: '⚠️',
      color: 'bg-muted-foreground'
    },
    {
      type: 'success',
      title: 'יעד הושג',
      message: 'הכנסות החודש עברו את היעד ב-8%',
      icon: '🎯',
      color: 'bg-green-500'
    },
    {
      type: 'info',
      title: 'תזרים מזומנים',
      message: 'צפוי מחסור במזומנים בעוד 10 יום',
      icon: '💰',
      color: 'bg-blue-500'
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentAlert(prev => (prev + 1) % alerts.length);
      setShowChart(true);
      setTimeout(() => setShowChart(false), 2000);
    }, 4000);

    return () => clearInterval(interval);
  }, [alerts.length]);

  return (
    <div className="flex flex-col lg:flex-row-reverse items-center gap-8 lg:gap-12">
      {/* Content */}
      <div className="flex-1 text-center lg:text-right">
        <div className="inline-flex items-center gap-2 bg-muted/20 text-muted-foreground px-3 py-1 rounded-full text-sm font-medium mb-4">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9.663 17H4.5C3.11929 17 2 15.8807 2 14.5C2 13.1193 3.11929 12 4.5 12C4.5 8.41015 7.41015 5.5 11 5.5C14.5899 5.5 17.5 8.41015 17.5 12C18.8807 12 20 13.1193 20 14.5C20 15.8807 18.8807 17 17.5 17H12.337" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M13 21L9 17L13 13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          ניתוח חכם
        </div>
        
        <h3 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
          דיווח AI מתקדם
        </h3>
        
        <p className="text-lg text-muted-foreground mb-6 leading-relaxed">
          סוכן AI שעוקב אחר הביצועים העסקיים שלכם, מזהה חריגות ומתריע על הזדמנויות 
          או בעיות פוטנציאליות לפני שהן הופכות לבעיה
        </p>

        <div className="space-y-4">
          <div className="flex items-center gap-3 justify-center lg:justify-start">
            <span className="text-foreground">זיהוי מוקדם של חריגות</span>
            <div className="h-8 w-8 rounded-full bg-muted/20 flex items-center justify-center">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-muted-foreground">
                <path d="M10.29 3.86L1.82 18C1.64486 18.3024 1.55674 18.6453 1.56554 18.9928C1.57434 19.3402 1.67967 19.6781 1.86795 19.9715C2.05623 20.2648 2.32295 20.5016 2.63695 20.6558C2.95095 20.8101 3.29983 20.8758 3.64 20.845H20.36C20.7002 20.8758 21.049 20.8101 21.363 20.6558C21.677 20.5016 21.9438 20.2648 22.132 19.9715C22.3203 19.6781 22.4257 19.3402 22.4345 18.9928C22.4433 18.6453 22.3551 18.3024 22.18 18L13.71 3.86C13.5317 3.56611 13.2807 3.32312 12.9812 3.15448C12.6817 2.98585 12.3438 2.89725 12 2.89725C11.6562 2.89725 11.3183 2.98585 11.0188 3.15448C10.7193 3.32312 10.4683 3.56611 10.29 3.86V3.86Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M12 9V13M12 17H12.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>

          <div className="flex items-center gap-3 justify-center lg:justify-start">
            <span className="text-foreground">ניתוח מגמות עסקיות</span>
            <div className="h-8 w-8 rounded-full bg-muted/20 flex items-center justify-center">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-muted-foreground">
                <path d="M18 20V10M12 20V4M6 20V14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>

          <div className="flex items-center gap-3 justify-center lg:justify-start">
            <span className="text-foreground">התראות בזמן אמת</span>
            <div className="h-8 w-8 rounded-full bg-muted/20 flex items-center justify-center">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-muted-foreground">
                <path d="M22 12H18L15 21L9 3L6 12H2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Animated Dashboard */}
      <div className="flex-shrink-0">
        <div className="relative">
          {/* Dashboard Frame */}
          <div className="w-96 h-80 bg-gradient-to-br from-gray-900 to-black rounded-2xl p-4 shadow-2xl">
            <div className="w-full h-full bg-gray-800 rounded-xl overflow-hidden relative">
              {/* Dashboard Header */}
              <div className="bg-gray-700 px-4 py-3 flex items-center justify-between">
                <h4 className="text-white font-medium">לוח בקרה AI</h4>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-400 text-xs">פעיל</span>
                </div>
              </div>

              {/* Charts Area */}
              <div className="p-4 space-y-4">
                {/* Mini Chart */}
                <div className="bg-gray-700 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white text-sm">הכנסות חודשיות</span>
                    <span className="text-green-400 text-xs">+12%</span>
                  </div>
                  <div className="flex items-end gap-1 h-12">
                    {[40, 65, 45, 80, 60, 90, 75].map((height, index) => (
                      <div
                        key={index}
                        className={`flex-1 rounded-t transition-all duration-1000 ${
                          showChart ? 'bg-blue-500' : 'bg-gray-600'
                        }`}
                        style={{ 
                          height: showChart ? `${height}%` : '20%',
                          transitionDelay: `${index * 100}ms`
                        }}
                      />
                    ))}
                  </div>
                </div>

                {/* Metrics */}
                <div className="grid grid-cols-2 gap-3">
                  <div className="bg-gray-700 rounded-lg p-3">
                    <div className="text-gray-400 text-xs mb-1">רווח נקי</div>
                    <div className="text-white font-bold">₪45,200</div>
                  </div>
                  <div className="bg-gray-700 rounded-lg p-3">
                    <div className="text-gray-400 text-xs mb-1">הוצאות</div>
                    <div className="text-white font-bold">₪12,800</div>
                  </div>
                </div>
              </div>

              {/* Alert Notification */}
              <div className={`absolute bottom-4 left-4 right-4 ${alerts[currentAlert].color} text-white p-3 rounded-lg shadow-lg transform transition-all duration-500 ${
                showChart ? 'translate-y-0 opacity-100' : 'translate-y-full opacity-0'
              }`}>
                <div className="flex items-start gap-3">
                  <span className="text-xl">{alerts[currentAlert].icon}</span>
                  <div className="flex-1">
                    <h5 className="font-medium text-sm">{alerts[currentAlert].title}</h5>
                    <p className="text-xs opacity-90 mt-1">{alerts[currentAlert].message}</p>
                  </div>
                  <button className="text-white/80 hover:text-white">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Floating AI Brain */}
          <div className="absolute -top-6 -left-6 w-16 h-16 bg-foreground rounded-full flex items-center justify-center shadow-lg">
            <svg width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-background">
              <path d="M9.663 17H4.5C3.11929 17 2 15.8807 2 14.5C2 13.1193 3.11929 12 4.5 12C4.5 8.41015 7.41015 5.5 11 5.5C14.5899 5.5 17.5 8.41015 17.5 12C18.8807 12 20 13.1193 20 14.5C20 15.8807 18.8807 17 17.5 17H12.337" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M13 21L9 17L13 13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>

          {/* Data Particles */}
          <div className="absolute top-1/4 -right-4 w-3 h-3 bg-muted-foreground rounded-full animate-ping"></div>
          <div className="absolute top-3/4 -left-2 w-2 h-2 bg-muted-foreground rounded-full animate-ping" style={{ animationDelay: '1s' }}></div>
          <div className="absolute bottom-1/4 -right-2 w-2 h-2 bg-muted-foreground rounded-full animate-ping" style={{ animationDelay: '2s' }}></div>
        </div>
      </div>
    </div>
  );
};

export default AIReportingFeature;
