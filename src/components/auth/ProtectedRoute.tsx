import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Loader2 } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requireOrganization?: boolean;
  requiredPermission?: string;
  requiredRole?: string;
  fallbackPath?: string;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAuth = true,
  requireOrganization = false,
  requiredPermission,
  requiredRole,
  fallbackPath = '/signin',
}) => {
  const {
    user,
    currentOrganization,
    userRole,
    loading,
    organizationsLoading,
    hasPermission
  } = useAuth();
  const location = useLocation();
  const [loadingTimeout, setLoadingTimeout] = React.useState(false);

  // Add timeout for loading states to prevent infinite loading
  React.useEffect(() => {
    if (loading || organizationsLoading) {
      const timer = setTimeout(() => {
        console.warn('⚠️ Loading timeout reached, forcing continue...');
        setLoadingTimeout(true);
      }, 8000); // 8 second timeout (reduced from 10)

      return () => clearTimeout(timer);
    } else {
      setLoadingTimeout(false);
    }
  }, [loading, organizationsLoading]);

  // Show loading spinner while checking authentication (with timeout)
  if ((loading || organizationsLoading) && !loadingTimeout) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin" />
          <p className="text-muted-foreground">טוען...</p>
          <p className="text-xs text-muted-foreground">
            {loading ? 'טוען מידע משתמש...' : 'טוען ארגונים...'}
          </p>
        </div>
      </div>
    );
  }

  // Check if authentication is required
  if (requireAuth && !user) {
    return <Navigate to={fallbackPath} state={{ from: location }} replace />;
  }

  // Check if organization is required
  if (requireOrganization && !currentOrganization) {
    // Double-check that user is actually authenticated before redirecting to onboarding
    if (!user) {
      console.log('🚫 No user found but organization required, redirecting to signin');
      return <Navigate to="/signin" state={{ from: location }} replace />;
    }
    console.log('🏢 No organization found, redirecting to organization setup');
    console.log('📊 Current organization:', currentOrganization);
    return <Navigate to="/onboarding/organization" state={{ from: location }} replace />;
  }

  // Check if specific permission is required
  if (requiredPermission && !hasPermission(requiredPermission)) {
    return <Navigate to="/unauthorized" state={{ from: location }} replace />;
  }

  // Check if specific role is required
  if (requiredRole && userRole !== requiredRole) {
    const roleHierarchy = {
      owner: 4,
      admin: 3,
      manager: 2,
      user: 1,
    };

    const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0;
    const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;

    if (userLevel < requiredLevel) {
      return <Navigate to="/unauthorized" state={{ from: location }} replace />;
    }
  }

  return <>{children}</>;
};

// Convenience components for common protection patterns
export const AuthenticatedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ProtectedRoute requireAuth={true}>
    {children}
  </ProtectedRoute>
);

export const OrganizationRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ProtectedRoute requireAuth={true} requireOrganization={true}>
    {children}
  </ProtectedRoute>
);

export const AdminRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ProtectedRoute requireAuth={true} requireOrganization={true} requiredPermission="admin">
    {children}
  </ProtectedRoute>
);

export const ManagerRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ProtectedRoute requireAuth={true} requireOrganization={true} requiredPermission="manage">
    {children}
  </ProtectedRoute>
);

// Public route component (redirects authenticated users)
export const PublicRoute: React.FC<{ 
  children: React.ReactNode; 
  redirectTo?: string;
}> = ({ children, redirectTo = '/dashboard' }) => {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin" />
          <p className="text-muted-foreground">טוען...</p>
        </div>
      </div>
    );
  }

  if (user) {
    return <Navigate to={redirectTo} replace />;
  }

  return <>{children}</>;
};
