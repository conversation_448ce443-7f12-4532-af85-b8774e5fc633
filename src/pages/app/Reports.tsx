import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AppShell from '@/components/layout/AppShell';
import PageHeader from '@/components/layout/PageHeader';
import {
  BarChart3,
  Download,
  Calendar,
  DollarSign,
  FileText,
  TrendingUp,
  TrendingDown,
  AlertCircle,
  CheckCircle,
  Clock,
  Target,
  PieChart,
  LineChart,
  Receipt,
  Users,
  Building2
} from 'lucide-react';

// Mock data for reports
const mockReportData = {
  monthlyIncome: [
    { month: 'ינואר', amount: 45000, documents: 12 },
    { month: 'פברואר', amount: 52000, documents: 15 },
    { month: 'מרץ', amount: 38000, documents: 10 },
    { month: 'אפריל', amount: 61000, documents: 18 },
    { month: 'מאי', amount: 47000, documents: 14 },
    { month: 'יוני', amount: 55000, documents: 16 },
    { month: 'יולי', amount: 49000, documents: 13 },
    { month: 'אוגוסט', amount: 58000, documents: 17 },
    { month: 'ספטמבר', amount: 43000, documents: 11 },
    { month: 'אוקטובר', amount: 67000, documents: 20 },
    { month: 'נובמבר', amount: 52000, documents: 15 },
    { month: 'דצמבר', amount: 45000, documents: 12 },
  ],
  monthlyExpenses: [
    { month: 'ינואר', amount: 18000, documents: 25 },
    { month: 'פברואר', amount: 22000, documents: 30 },
    { month: 'מרץ', amount: 16000, documents: 20 },
    { month: 'אפריל', amount: 25000, documents: 35 },
    { month: 'מאי', amount: 19000, documents: 28 },
    { month: 'יוני', amount: 21000, documents: 32 },
    { month: 'יולי', amount: 20000, documents: 29 },
    { month: 'אוגוסט', amount: 23000, documents: 33 },
    { month: 'ספטמבר', amount: 17000, documents: 22 },
    { month: 'אוקטובר', amount: 26000, documents: 38 },
    { month: 'נובמבר', amount: 21000, documents: 31 },
    { month: 'דצמבר', amount: 18000, documents: 26 },
  ],
  documentStatus: {
    sent: 45,
    paid: 38,
    pending: 12,
    overdue: 5,
    draft: 8,
  },
  topClients: [
    { name: 'ABC טכנולוגיות', revenue: 125000, documents: 15 },
    { name: 'XYZ פיתוח', revenue: 89000, documents: 12 },
    { name: 'DEF עסקים', revenue: 67000, documents: 9 },
    { name: 'GHI סטארטאפ', revenue: 45000, documents: 7 },
    { name: 'JKL חברה', revenue: 32000, documents: 5 },
  ],
  taxObligations: [
    {
      type: 'דוח מע"מ',
      period: 'נובמבר 2024',
      dueDate: '2024-12-15',
      status: 'pending',
      amount: 8500,
    },
    {
      type: 'מס הכנסה רבעוני',
      period: 'Q4 2024',
      dueDate: '2024-12-31',
      status: 'draft',
      amount: 15200,
    },
    {
      type: 'דוח מע"מ',
      period: 'דצמבר 2024',
      dueDate: '2025-01-15',
      status: 'upcoming',
      amount: 9200,
    },
  ],
};

const Reports: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('2024');
  const [selectedReport, setSelectedReport] = useState('overview');

  const currentYear = new Date().getFullYear();
  const totalIncome = mockReportData.monthlyIncome.reduce((sum, month) => sum + month.amount, 0);
  const totalExpenses = mockReportData.monthlyExpenses.reduce((sum, month) => sum + month.amount, 0);
  const netProfit = totalIncome - totalExpenses;
  const profitMargin = ((netProfit / totalIncome) * 100).toFixed(1);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('he-IL', {
      style: 'currency',
      currency: 'ILS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('he-IL');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-muted-foreground';
      case 'draft': return 'bg-gray-500';
      case 'upcoming': return 'bg-blue-500';
      case 'completed': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending': return 'ממתין';
      case 'draft': return 'טיוטה';
      case 'upcoming': return 'קרוב';
      case 'completed': return 'הושלם';
      default: return status;
    }
  };

  return (
    <AppShell>
      <div className="space-y-6">
        <PageHeader
          title="דוחות וניתוחים"
          subtitle="ניתוח ביצועים עסקיים ודוחות מס"
          breadcrumbs={[
            { label: 'לוח בקרה', href: '/dashboard' },
            { label: 'דוחות' }
          ]}
          actions={
            <div className="flex items-center gap-3">
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="2024">2024</SelectItem>
                  <SelectItem value="2023">2023</SelectItem>
                  <SelectItem value="2022">2022</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" className="gap-2">
                <Download className="h-4 w-4" />
                ייצוא דוח
              </Button>
            </div>
          }
        />

        {/* Key Metrics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card className="cosmic-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">סה"כ הכנסות</p>
                  <p className="text-2xl font-bold">{formatCurrency(totalIncome)}</p>
                  <div className="flex items-center gap-1 mt-1">
                    <TrendingUp className="h-4 w-4 text-green-500" />
                    <span className="text-sm text-green-500">+12.5%</span>
                  </div>
                </div>
                <DollarSign className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="cosmic-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">סה"כ הוצאות</p>
                  <p className="text-2xl font-bold">{formatCurrency(totalExpenses)}</p>
                  <div className="flex items-center gap-1 mt-1">
                    <TrendingDown className="h-4 w-4 text-red-500" />
                    <span className="text-sm text-red-500">+8.2%</span>
                  </div>
                </div>
                <Receipt className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="cosmic-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">רווח נקי</p>
                  <p className="text-2xl font-bold">{formatCurrency(netProfit)}</p>
                  <div className="flex items-center gap-1 mt-1">
                    <TrendingUp className="h-4 w-4 text-green-500" />
                    <span className="text-sm text-green-500">{profitMargin}%</span>
                  </div>
                </div>
                <Target className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="cosmic-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">מסמכים פעילים</p>
                  <p className="text-2xl font-bold">
                    {Object.values(mockReportData.documentStatus).reduce((a, b) => a + b, 0)}
                  </p>
                  <div className="flex items-center gap-1 mt-1">
                    <FileText className="h-4 w-4 text-blue-500" />
                    <span className="text-sm text-blue-500">+5 החודש</span>
                  </div>
                </div>
                <FileText className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts and Analytics */}
        <Tabs value={selectedReport} onValueChange={setSelectedReport}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">סקירה כללית</TabsTrigger>
            <TabsTrigger value="income-expenses">הכנסות והוצאות</TabsTrigger>
            <TabsTrigger value="clients">ניתוח לקוחות</TabsTrigger>
            <TabsTrigger value="tax">חובות מס</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid gap-6 lg:grid-cols-2">
              {/* Income vs Expenses Chart Placeholder */}
              <Card className="cosmic-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    הכנסות מול הוצאות
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
                    <div className="text-center">
                      <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                      <p className="text-muted-foreground">תרשים הכנסות מול הוצאות</p>
                      <p className="text-sm text-muted-foreground">יוצג כאן עם ספריית תרשימים</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Document Status Distribution */}
              <Card className="cosmic-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <PieChart className="h-5 w-5" />
                    סטטוס מסמכים
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {Object.entries(mockReportData.documentStatus).map(([status, count]) => {
                      const percentage = ((count / Object.values(mockReportData.documentStatus).reduce((a, b) => a + b, 0)) * 100).toFixed(1);
                      const statusLabels = {
                        sent: 'נשלח',
                        paid: 'שולם',
                        pending: 'ממתין',
                        overdue: 'פג תוקף',
                        draft: 'טיוטה',
                      };
                      const statusColors = {
                        sent: 'bg-blue-500',
                        paid: 'bg-green-500',
                        pending: 'bg-yellow-500',
                        overdue: 'bg-red-500',
                        draft: 'bg-gray-500',
                      };
                      
                      return (
                        <div key={status} className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className={`w-3 h-3 rounded-full ${statusColors[status as keyof typeof statusColors]}`}></div>
                            <span className="text-sm">{statusLabels[status as keyof typeof statusLabels]}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium">{count}</span>
                            <span className="text-xs text-muted-foreground">({percentage}%)</span>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Monthly Trends */}
            <Card className="cosmic-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <LineChart className="h-5 w-5" />
                  מגמות חודשיות
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80 flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg">
                  <div className="text-center">
                    <LineChart className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                    <p className="text-lg text-muted-foreground">תרשים מגמות חודשיות</p>
                    <p className="text-sm text-muted-foreground">יוצג כאן עם ספריית תרשימים כמו Chart.js או Recharts</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="income-expenses" className="space-y-6">
            <div className="grid gap-6 lg:grid-cols-2">
              <Card className="cosmic-card">
                <CardHeader>
                  <CardTitle>הכנסות חודשיות</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {mockReportData.monthlyIncome.slice(-6).map((month, index) => (
                      <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-accent/20">
                        <span className="font-medium">{month.month}</span>
                        <div className="text-left">
                          <div className="font-bold">{formatCurrency(month.amount)}</div>
                          <div className="text-sm text-muted-foreground">{month.documents} מסמכים</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="cosmic-card">
                <CardHeader>
                  <CardTitle>הוצאות חודשיות</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {mockReportData.monthlyExpenses.slice(-6).map((month, index) => (
                      <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-accent/20">
                        <span className="font-medium">{month.month}</span>
                        <div className="text-left">
                          <div className="font-bold">{formatCurrency(month.amount)}</div>
                          <div className="text-sm text-muted-foreground">{month.documents} מסמכים</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="clients" className="space-y-6">
            <Card className="cosmic-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  לקוחות מובילים
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockReportData.topClients.map((client, index) => (
                    <div key={index} className="flex items-center justify-between p-4 rounded-lg border">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                          <span className="text-sm font-bold">{index + 1}</span>
                        </div>
                        <div>
                          <div className="font-medium">{client.name}</div>
                          <div className="text-sm text-muted-foreground">{client.documents} מסמכים</div>
                        </div>
                      </div>
                      <div className="text-left">
                        <div className="font-bold">{formatCurrency(client.revenue)}</div>
                        <div className="text-sm text-muted-foreground">הכנסות</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="tax" className="space-y-6">
            <Card className="cosmic-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5" />
                  חובות מס קרובים
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockReportData.taxObligations.map((obligation, index) => (
                    <div key={index} className="flex items-center justify-between p-4 rounded-lg border">
                      <div className="flex items-center gap-3">
                        <div className={`w-3 h-3 rounded-full ${getStatusColor(obligation.status)}`}></div>
                        <div>
                          <div className="font-medium">{obligation.type}</div>
                          <div className="text-sm text-muted-foreground">{obligation.period}</div>
                        </div>
                      </div>
                      <div className="text-left">
                        <div className="font-bold">{formatCurrency(obligation.amount)}</div>
                        <div className="text-sm text-muted-foreground">
                          יעד: {formatDate(obligation.dueDate)}
                        </div>
                        <Badge variant="outline" className="mt-1">
                          {getStatusLabel(obligation.status)}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AppShell>
  );
};

export default Reports;
