import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AppShell from '@/components/layout/AppShell';
import PageHeader from '@/components/layout/PageHeader';
import {
  Users,
  Search,
  Filter,
  Plus,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Phone,
  Mail,
  MapPin,
  Building2,
  FileText,
  Calendar,
  DollarSign,
  TrendingUp,
  Clock
} from 'lucide-react';

// Mock data for clients
const mockClients = [
  {
    id: '1',
    name: 'ABC טכנולוגיות בע"מ',
    contactPerson: 'יוסי כהן',
    email: '<EMAIL>',
    phone: '03-1234567',
    mobile: '050-1234567',
    address: 'רחוב הרצל 123, תל אביב',
    taxId: '*********',
    type: 'business',
    status: 'active',
    totalRevenue: 45280,
    documentsCount: 12,
    lastActivity: '2024-12-01',
    createdAt: '2024-01-15',
    notes: 'לקוח VIP - תנאי תשלום מיוחדים',
  },
  {
    id: '2',
    name: 'XYZ חברה לפיתוח',
    contactPerson: 'שרה לוי',
    email: '<EMAIL>',
    phone: '09-8765432',
    mobile: '052-9876543',
    address: 'שדרות רוטשילד 45, תל אביב',
    taxId: '*********',
    type: 'business',
    status: 'active',
    totalRevenue: 28500,
    documentsCount: 8,
    lastActivity: '2024-11-28',
    createdAt: '2024-03-20',
    notes: 'פרויקטים חוזרים',
  },
  {
    id: '3',
    name: 'דוד ישראלי',
    contactPerson: 'דוד ישראלי',
    email: '<EMAIL>',
    phone: '',
    mobile: '054-1111111',
    address: 'רחוב הנביאים 67, ירושלים',
    taxId: '',
    type: 'individual',
    status: 'active',
    totalRevenue: 5200,
    documentsCount: 3,
    lastActivity: '2024-11-25',
    createdAt: '2024-06-10',
    notes: 'לקוח פרטי - שירותי ייעוץ',
  },
  {
    id: '4',
    name: 'GHI סטארטאפ',
    contactPerson: 'מיכל גרין',
    email: '<EMAIL>',
    phone: '04-5555555',
    mobile: '053-5555555',
    address: 'פארק הייטק, חיפה',
    taxId: '*********',
    type: 'business',
    status: 'inactive',
    totalRevenue: 12000,
    documentsCount: 5,
    lastActivity: '2024-09-15',
    createdAt: '2024-02-01',
    notes: 'פרויקט הושלם',
  },
];

const Clients: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [selectedClient, setSelectedClient] = useState<typeof mockClients[0] | null>(null);

  const statusConfig = {
    active: { label: 'פעיל', variant: 'default' as const, color: 'bg-green-500' },
    inactive: { label: 'לא פעיל', variant: 'secondary' as const, color: 'bg-gray-500' },
    pending: { label: 'ממתין', variant: 'secondary' as const, color: 'bg-muted-foreground' },
  };

  const typeConfig = {
    business: { label: 'עסק', icon: Building2 },
    individual: { label: 'פרטי', icon: Users },
  };

  const filteredClients = mockClients.filter(client => {
    const matchesSearch = client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         client.contactPerson.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         client.email.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || client.status === statusFilter;
    const matchesType = typeFilter === 'all' || client.type === typeFilter;
    
    return matchesSearch && matchesStatus && matchesType;
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('he-IL', {
      style: 'currency',
      currency: 'ILS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('he-IL');
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(word => word.charAt(0)).join('').slice(0, 2).toUpperCase();
  };

  return (
    <AppShell>
      <div className="space-y-6">
        <PageHeader
          title="לקוחות"
          subtitle="נהל את פרטי הלקוחות והיסטוריית המסמכים שלהם"
          breadcrumbs={[
            { label: 'לוח בקרה', href: '/dashboard' },
            { label: 'לקוחות' }
          ]}
          actions={
            <div className="flex items-center gap-3">
              <Button variant="outline" className="gap-2">
                <FileText className="h-4 w-4" />
                ייצוא רשימה
              </Button>
              <Button className="gap-2">
                <Plus className="h-4 w-4" />
                לקוח חדש
              </Button>
            </div>
          }
        />

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card className="cosmic-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">סה"כ לקוחות</p>
                  <p className="text-2xl font-bold">{mockClients.length}</p>
                </div>
                <Users className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>

          <Card className="cosmic-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">לקוחות פעילים</p>
                  <p className="text-2xl font-bold">
                    {mockClients.filter(c => c.status === 'active').length}
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="cosmic-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">סה"כ הכנסות</p>
                  <p className="text-2xl font-bold">
                    {formatCurrency(mockClients.reduce((sum, client) => sum + client.totalRevenue, 0))}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="cosmic-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">מסמכים החודש</p>
                  <p className="text-2xl font-bold">
                    {mockClients.reduce((sum, client) => sum + client.documentsCount, 0)}
                  </p>
                </div>
                <FileText className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card className="cosmic-card">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="חיפוש לקוחות..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="gap-2">
                      <Filter className="h-4 w-4" />
                      סטטוס
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => setStatusFilter('all')}>
                      כל הסטטוסים
                    </DropdownMenuItem>
                    {Object.entries(statusConfig).map(([status, config]) => (
                      <DropdownMenuItem key={status} onClick={() => setStatusFilter(status)}>
                        <div className={`w-2 h-2 rounded-full ${config.color} ml-2`}></div>
                        {config.label}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="gap-2">
                      <Building2 className="h-4 w-4" />
                      סוג
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => setTypeFilter('all')}>
                      כל הסוגים
                    </DropdownMenuItem>
                    {Object.entries(typeConfig).map(([type, config]) => (
                      <DropdownMenuItem key={type} onClick={() => setTypeFilter(type)}>
                        <config.icon className="ml-2 h-4 w-4" />
                        {config.label}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Clients Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredClients.map((client) => {
            const statusInfo = statusConfig[client.status as keyof typeof statusConfig];
            const typeInfo = typeConfig[client.type as keyof typeof typeConfig];
            
            return (
              <Card key={client.id} className="cosmic-card hover:cosmic-glow transition-all duration-300">
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-12 w-12">
                        <AvatarImage src="" alt={client.name} />
                        <AvatarFallback className="text-sm font-medium">
                          {getInitials(client.name)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-semibold text-foreground truncate">{client.name}</h3>
                        <p className="text-sm text-muted-foreground truncate">{client.contactPerson}</p>
                      </div>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => setSelectedClient(client)}>
                          <Eye className="ml-2 h-4 w-4" />
                          צפייה
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Edit className="ml-2 h-4 w-4" />
                          עריכה
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <FileText className="ml-2 h-4 w-4" />
                          מסמך חדש
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-destructive">
                          <Trash2 className="ml-2 h-4 w-4" />
                          מחיקה
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Badge variant={statusInfo.variant} className="gap-1">
                      <div className={`w-2 h-2 rounded-full ${statusInfo.color}`}></div>
                      {statusInfo.label}
                    </Badge>
                    <Badge variant="outline" className="gap-1">
                      <typeInfo.icon className="h-3 w-3" />
                      {typeInfo.label}
                    </Badge>
                  </div>

                  <div className="space-y-2 text-sm">
                    {client.email && (
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <Mail className="h-4 w-4" />
                        <span className="truncate">{client.email}</span>
                      </div>
                    )}
                    {client.phone && (
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <Phone className="h-4 w-4" />
                        <span>{client.phone}</span>
                      </div>
                    )}
                    {client.address && (
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <MapPin className="h-4 w-4" />
                        <span className="truncate">{client.address}</span>
                      </div>
                    )}
                  </div>

                  <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                    <div className="text-center">
                      <p className="text-lg font-semibold">{formatCurrency(client.totalRevenue)}</p>
                      <p className="text-xs text-muted-foreground">סה"כ הכנסות</p>
                    </div>
                    <div className="text-center">
                      <p className="text-lg font-semibold">{client.documentsCount}</p>
                      <p className="text-xs text-muted-foreground">מסמכים</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    <span>פעילות אחרונה: {formatDate(client.lastActivity)}</span>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Client Details Dialog */}
        <Dialog open={!!selectedClient} onOpenChange={() => setSelectedClient(null)}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            {selectedClient && (
              <>
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-3">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src="" alt={selectedClient.name} />
                      <AvatarFallback>
                        {getInitials(selectedClient.name)}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h2 className="text-xl font-bold">{selectedClient.name}</h2>
                      <p className="text-muted-foreground">{selectedClient.contactPerson}</p>
                    </div>
                  </DialogTitle>
                  <DialogDescription>
                    פרטי לקוח והיסטוריית פעילות
                  </DialogDescription>
                </DialogHeader>

                <Tabs defaultValue="details" className="mt-6">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="details">פרטים</TabsTrigger>
                    <TabsTrigger value="documents">מסמכים</TabsTrigger>
                    <TabsTrigger value="activity">פעילות</TabsTrigger>
                  </TabsList>

                  <TabsContent value="details" className="mt-6">
                    <div className="grid gap-6 md:grid-cols-2">
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg">פרטי קשר</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="space-y-2">
                            <Label className="text-sm font-medium">אימייל</Label>
                            <p className="text-sm">{selectedClient.email || 'לא צוין'}</p>
                          </div>
                          <div className="space-y-2">
                            <Label className="text-sm font-medium">טלפון</Label>
                            <p className="text-sm">{selectedClient.phone || 'לא צוין'}</p>
                          </div>
                          <div className="space-y-2">
                            <Label className="text-sm font-medium">נייד</Label>
                            <p className="text-sm">{selectedClient.mobile || 'לא צוין'}</p>
                          </div>
                          <div className="space-y-2">
                            <Label className="text-sm font-medium">כתובת</Label>
                            <p className="text-sm">{selectedClient.address || 'לא צוינה'}</p>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg">פרטים עסקיים</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="space-y-2">
                            <Label className="text-sm font-medium">מספר עוסק מורשה</Label>
                            <p className="text-sm">{selectedClient.taxId || 'לא צוין'}</p>
                          </div>
                          <div className="space-y-2">
                            <Label className="text-sm font-medium">סוג לקוח</Label>
                            <p className="text-sm">{typeConfig[selectedClient.type as keyof typeof typeConfig].label}</p>
                          </div>
                          <div className="space-y-2">
                            <Label className="text-sm font-medium">סטטוס</Label>
                            <Badge variant={statusConfig[selectedClient.status as keyof typeof statusConfig].variant}>
                              {statusConfig[selectedClient.status as keyof typeof statusConfig].label}
                            </Badge>
                          </div>
                          <div className="space-y-2">
                            <Label className="text-sm font-medium">תאריך הצטרפות</Label>
                            <p className="text-sm">{formatDate(selectedClient.createdAt)}</p>
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    {selectedClient.notes && (
                      <Card className="mt-6">
                        <CardHeader>
                          <CardTitle className="text-lg">הערות</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm">{selectedClient.notes}</p>
                        </CardContent>
                      </Card>
                    )}
                  </TabsContent>

                  <TabsContent value="documents" className="mt-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">מסמכים אחרונים</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-muted-foreground text-center py-8">
                          רשימת המסמכים תוצג כאן
                        </p>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="activity" className="mt-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">היסטוריית פעילות</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-muted-foreground text-center py-8">
                          היסטוריית הפעילות תוצג כאן
                        </p>
                      </CardContent>
                    </Card>
                  </TabsContent>
                </Tabs>
              </>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </AppShell>
  );
};

export default Clients;
