-- Add survey fields to user_profiles table
-- These fields store data collected during the signup survey

ALTER TABLE user_profiles 
ADD COLUMN company_name VA<PERSON><PERSON><PERSON>(255),
ADD COLUMN annual_revenue VARCHAR(50),
ADD COLUMN accounting_services BOOLEAN DEFAULT false,
ADD COLUMN business_loan BOOLEAN DEFAULT false;

-- Update the user profile creation trigger to handle the new fields
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_profiles (
    id, 
    email, 
    first_name, 
    last_name,
    company_name,
    annual_revenue,
    accounting_services,
    business_loan
  )
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'first_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'last_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'company_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'annual_revenue', ''),
    COALESCE((NEW.raw_user_meta_data->>'accounting_services')::boolean, false),
    COALESCE((NEW.raw_user_meta_data->>'business_loan')::boolean, false)
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
