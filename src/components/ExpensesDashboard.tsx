import React from 'react';

interface ExpenseItem {
  id: string;
  title: string;
  description: string;
  amount: number;
  category: string;
  date: string;
  vendor: string;
  status: 'pending' | 'approved' | 'rejected';
}

const ExpensesDashboard: React.FC = () => {
  const expenses: ExpenseItem[] = [
    {
      id: 'exp1',
      title: 'ציוד משרדי',
      description: 'מחשב נייד חדש לפיתוח',
      amount: 8500,
      category: 'ציוד',
      date: '15 יוני',
      vendor: 'חנות מחשבים',
      status: 'approved'
    },
    {
      id: 'exp2',
      title: 'נסיעות עסקיות',
      description: 'נסיעה לפגישה עם לקוח',
      amount: 450,
      category: 'נסיעות',
      date: '12 יוני',
      vendor: 'חברת מוניות',
      status: 'pending'
    },
    {
      id: 'exp3',
      title: 'תוכנות ורישיונות',
      description: 'מנוי שנתי Adobe Creative Suite',
      amount: 2400,
      category: 'תוכנה',
      date: '10 יוני',
      vendor: 'Adobe',
      status: 'approved'
    },
    {
      id: 'exp4',
      title: 'ארוח<PERSON><PERSON> עסקיות',
      description: 'ארוחת צהריים עם לקוח פוטנציאלי',
      amount: 280,
      category: 'אוכל',
      date: '8 יוני',
      vendor: 'מסעדת השף',
      status: 'rejected'
    }
  ];

  const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);
  const approvedExpenses = expenses.filter(exp => exp.status === 'approved').reduce((sum, exp) => sum + exp.amount, 0);
  const pendingExpenses = expenses.filter(exp => exp.status === 'pending').reduce((sum, exp) => sum + exp.amount, 0);

  const formatAmount = (amount: number) => {
    return amount.toLocaleString('he-IL');
  };

  const getStatusColor = (status: string) => {
    const colors = {
      'approved': 'text-green-400 bg-green-500/10 border-green-500/20',
      'pending': 'text-muted-foreground bg-muted/20 border-muted/30',
      'rejected': 'text-red-400 bg-red-500/10 border-red-500/20'
    };
    return colors[status as keyof typeof colors] || 'text-muted-foreground bg-muted/10';
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      'ציוד': 'text-blue-400 bg-blue-500/10 border-blue-500/20',
      'נסיעות': 'text-purple-400 bg-purple-500/10 border-purple-500/20',
      'תוכנה': 'text-green-400 bg-green-500/10 border-green-500/20',
      'אוכל': 'text-orange-400 bg-orange-500/10 border-orange-500/20'
    };
    return colors[category as keyof typeof colors] || 'text-muted-foreground bg-muted/10';
  };

  return (
    <div className="p-4 space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-card rounded-lg border border-border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">סך הוצאות החודש</p>
              <p className="text-2xl font-bold text-foreground">{formatAmount(totalExpenses)} ₪</p>
            </div>
            <div className="h-12 w-12 rounded-lg bg-blue-500/10 flex items-center justify-center">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-blue-400">
                <path d="M12 2V22M17 5H9.5C8.57174 5 7.6815 5.36875 7.02513 6.02513C6.36875 6.6815 6 7.57174 6 8.5C6 9.42826 6.36875 10.3185 7.02513 10.9749C7.6815 11.6312 8.57174 12 9.5 12H14.5C15.4283 12 16.3185 12.3687 16.9749 13.0251C17.6312 13.6815 18 14.5717 18 15.5C18 16.4283 17.6312 17.3185 16.9749 17.9749C16.3185 18.6312 15.4283 19 14.5 19H6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-card rounded-lg border border-border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">הוצאות מאושרות</p>
              <p className="text-2xl font-bold text-green-400">{formatAmount(approvedExpenses)} ₪</p>
            </div>
            <div className="h-12 w-12 rounded-lg bg-green-500/10 flex items-center justify-center">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-green-400">
                <path d="M5 12L10 17L19 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-card rounded-lg border border-border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">ממתינות לאישור</p>
              <p className="text-2xl font-bold text-muted-foreground">{formatAmount(pendingExpenses)} ₪</p>
            </div>
            <div className="h-12 w-12 rounded-lg bg-muted/20 flex items-center justify-center">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-muted-foreground">
                <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                <path d="M12 6V12L16 14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Expenses List */}
      <div className="bg-card rounded-lg border border-border">
        <div className="p-4 border-b border-border">
          <h3 className="font-medium text-foreground">הוצאות אחרונות</h3>
        </div>
        <div className="divide-y divide-border">
          {expenses.map((expense) => (
            <div key={expense.id} className="p-4 hover:bg-muted/20 transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h4 className="font-medium text-foreground">{expense.title}</h4>
                    <span className={`text-xs px-2 py-1 rounded-full border ${getCategoryColor(expense.category)}`}>
                      {expense.category}
                    </span>
                    <span className={`text-xs px-2 py-1 rounded-full border ${getStatusColor(expense.status)}`}>
                      {expense.status === 'approved' ? 'מאושר' : expense.status === 'pending' ? 'ממתין' : 'נדחה'}
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground mb-1">{expense.description}</p>
                  <p className="text-xs text-muted-foreground">{expense.vendor} • {expense.date}</p>
                </div>
                <div className="text-left">
                  <p className="text-lg font-bold text-foreground">{formatAmount(expense.amount)} ₪</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ExpensesDashboard;
