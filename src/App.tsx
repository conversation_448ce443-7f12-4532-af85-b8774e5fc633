import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "next-themes";
import { AuthProvider } from "@/contexts/AuthContext";
import { ErrorBoundary, AuthErrorBoundary } from "@/components/ErrorBoundary";
import { PublicRoute, AuthenticatedRoute, OrganizationRoute } from "@/components/auth/ProtectedRoute";
import DebugPanel from "@/components/DebugPanel";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import SignIn from "./pages/auth/SignIn";
import SignUp from "./pages/auth/SignUp";
import ForgotPassword from "./pages/auth/ForgotPassword";
import Dashboard from "./pages/app/Dashboard";
import Documents from "./pages/app/Documents";
import CreateDocument from "./pages/app/CreateDocument";
import Clients from "./pages/app/Clients";
import Expenses from "./pages/app/Expenses";
import Reports from "./pages/app/Reports";
import Settings from "./pages/app/Settings";
import Unauthorized from "./pages/Unauthorized";
import OrganizationSetup from "./pages/onboarding/OrganizationSetup";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error: any) => {
        // Don't retry on auth errors
        if (error?.status === 401 || error?.status === 403) {
          return false;
        }
        return failureCount < 3;
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

const App = () => (
  <ErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
        <TooltipProvider>
          <AuthErrorBoundary>
            <AuthProvider>
              <Toaster />
              <Sonner />
              <BrowserRouter>
                <Routes>
                  {/* Public routes */}
                  <Route path="/" element={
                    <PublicRoute>
                      <Index />
                    </PublicRoute>
                  } />

                  {/* Authentication routes */}
                  <Route path="/signin" element={
                    <PublicRoute>
                      <SignIn />
                    </PublicRoute>
                  } />
                  <Route path="/signup" element={
                    <PublicRoute>
                      <SignUp />
                    </PublicRoute>
                  } />
                  <Route path="/forgot-password" element={
                    <PublicRoute>
                      <ForgotPassword />
                    </PublicRoute>
                  } />

                  {/* Onboarding routes */}
                  <Route path="/onboarding/organization" element={
                    <AuthenticatedRoute>
                      <OrganizationSetup />
                    </AuthenticatedRoute>
                  } />

                  {/* Protected application routes */}
                  <Route path="/dashboard" element={
                    <OrganizationRoute>
                      <Dashboard />
                    </OrganizationRoute>
                  } />

                  {/* Documents routes */}
                  <Route path="/app/documents" element={
                    <OrganizationRoute>
                      <Documents />
                    </OrganizationRoute>
                  } />
                  <Route path="/app/documents/new" element={
                    <OrganizationRoute>
                      <CreateDocument />
                    </OrganizationRoute>
                  } />
                  <Route path="/app/documents/:id/edit" element={
                    <OrganizationRoute>
                      <CreateDocument />
                    </OrganizationRoute>
                  } />

                  {/* Clients routes */}
                  <Route path="/app/clients" element={
                    <OrganizationRoute>
                      <Clients />
                    </OrganizationRoute>
                  } />

                  {/* Expenses routes */}
                  <Route path="/app/expenses" element={
                    <OrganizationRoute>
                      <Expenses />
                    </OrganizationRoute>
                  } />
                  <Route path="/app/expenses/new" element={
                    <OrganizationRoute>
                      <Expenses />
                    </OrganizationRoute>
                  } />

                  {/* Reports routes */}
                  <Route path="/app/reports" element={
                    <OrganizationRoute>
                      <Reports />
                    </OrganizationRoute>
                  } />

                  {/* Settings routes */}
                  <Route path="/app/settings" element={
                    <OrganizationRoute>
                      <Settings />
                    </OrganizationRoute>
                  } />

                  {/* Utility routes */}
                  <Route path="/unauthorized" element={<Unauthorized />} />

                  {/* Catch-all route */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </BrowserRouter>
              <DebugPanel />
            </AuthProvider>
          </AuthErrorBoundary>
        </TooltipProvider>
      </ThemeProvider>
    </QueryClientProvider>
  </ErrorBoundary>
);

export default App;
