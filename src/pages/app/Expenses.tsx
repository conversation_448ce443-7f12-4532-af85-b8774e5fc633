import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import AppShell from '@/components/layout/AppShell';
import PageHeader from '@/components/layout/PageHeader';
import {
  Receipt,
  Upload,
  Camera,
  Mail,
  Search,
  Filter,
  Plus,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Check,
  X,
  Clock,
  AlertCircle,
  FileText,
  Image,
  Calendar,
  DollarSign,
  Tag,
  CheckCircle,
  XCircle
} from 'lucide-react';

// Mock data for expenses
const mockExpenses = [
  {
    id: '1',
    title: 'דלק לרכב החברה',
    amount: 180,
    currency: 'ILS',
    category: 'transportation',
    date: '2024-12-01',
    status: 'approved',
    receipt: 'receipt-001.jpg',
    description: 'תדלוק ברחוב הרצל',
    vendor: 'תחנת דלק פז',
    submittedBy: 'יוסי כהן',
    submittedAt: '2024-12-01T10:30:00',
    approvedBy: 'שרה לוי',
    approvedAt: '2024-12-01T14:20:00',
  },
  {
    id: '2',
    title: 'ארוחת עסקים עם לקוח',
    amount: 320,
    currency: 'ILS',
    category: 'meals',
    date: '2024-11-30',
    status: 'pending',
    receipt: 'receipt-002.jpg',
    description: 'פגישה עם לקוח ABC',
    vendor: 'מסעדת הבשר',
    submittedBy: 'דוד ישראלי',
    submittedAt: '2024-11-30T20:15:00',
  },
  {
    id: '3',
    title: 'ציוד משרדי',
    amount: 450,
    currency: 'ILS',
    category: 'office',
    date: '2024-11-28',
    status: 'rejected',
    receipt: 'receipt-003.jpg',
    description: 'נייר ועטים',
    vendor: 'אופיס דיפו',
    submittedBy: 'מיכל גרין',
    submittedAt: '2024-11-28T16:45:00',
    rejectedBy: 'שרה לוי',
    rejectedAt: '2024-11-29T09:30:00',
    rejectionReason: 'לא מאושר מראש',
  },
  {
    id: '4',
    title: 'מנוי תוכנה',
    amount: 99,
    currency: 'USD',
    category: 'software',
    date: '2024-11-25',
    status: 'draft',
    receipt: null,
    description: 'מנוי חודשי ל-Figma',
    vendor: 'Figma Inc.',
    submittedBy: 'יוסי כהן',
    submittedAt: '2024-11-25T11:00:00',
  },
];

const categories = {
  transportation: { label: 'תחבורה', color: 'bg-blue-500', icon: '🚗' },
  meals: { label: 'ארוחות', color: 'bg-green-500', icon: '🍽️' },
  office: { label: 'משרד', color: 'bg-purple-500', icon: '🏢' },
  software: { label: 'תוכנה', color: 'bg-orange-500', icon: '💻' },
  travel: { label: 'נסיעות', color: 'bg-red-500', icon: '✈️' },
  marketing: { label: 'שיווק', color: 'bg-pink-500', icon: '📢' },
  other: { label: 'אחר', color: 'bg-gray-500', icon: '📋' },
};

const statusConfig = {
  draft: { label: 'טיוטה', variant: 'secondary' as const, icon: Edit, color: 'bg-gray-500' },
  pending: { label: 'ממתין לאישור', variant: 'secondary' as const, icon: Clock, color: 'bg-muted-foreground' },
  approved: { label: 'אושר', variant: 'default' as const, icon: CheckCircle, color: 'bg-green-500' },
  rejected: { label: 'נדחה', variant: 'destructive' as const, icon: XCircle, color: 'bg-red-500' },
};

const Expenses: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [selectedExpense, setSelectedExpense] = useState<typeof mockExpenses[0] | null>(null);
  const [isNewExpenseOpen, setIsNewExpenseOpen] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setUploadedFiles(prev => [...prev, ...acceptedFiles]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif'],
      'application/pdf': ['.pdf'],
    },
    multiple: true,
  });

  const filteredExpenses = mockExpenses.filter(expense => {
    const matchesSearch = expense.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         expense.vendor.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         expense.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || expense.status === statusFilter;
    const matchesCategory = categoryFilter === 'all' || expense.category === categoryFilter;
    
    return matchesSearch && matchesStatus && matchesCategory;
  });

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('he-IL', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('he-IL');
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('he-IL');
  };

  const getTotalByStatus = (status: string) => {
    return mockExpenses
      .filter(expense => expense.status === status)
      .reduce((sum, expense) => {
        // Convert USD to ILS for calculation (simplified)
        const amount = expense.currency === 'USD' ? expense.amount * 3.7 : expense.amount;
        return sum + amount;
      }, 0);
  };

  return (
    <AppShell>
      <div className="space-y-6">
        <PageHeader
          title="הוצאות"
          subtitle="נהל הוצאות, העלה קבלות ואשר תשלומים"
          breadcrumbs={[
            { label: 'לוח בקרה', href: '/dashboard' },
            { label: 'הוצאות' }
          ]}
          actions={
            <div className="flex items-center gap-3">
              <Button variant="outline" className="gap-2">
                <Mail className="h-4 w-4" />
                סנכרון Gmail
              </Button>
              <Dialog open={isNewExpenseOpen} onOpenChange={setIsNewExpenseOpen}>
                <DialogTrigger asChild>
                  <Button className="gap-2">
                    <Plus className="h-4 w-4" />
                    הוצאה חדשה
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>הוצאה חדשה</DialogTitle>
                    <DialogDescription>
                      הוסף הוצאה חדשה עם קבלה
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-6 mt-6">
                    {/* Upload Area */}
                    <div
                      {...getRootProps()}
                      className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                        isDragActive ? 'border-primary bg-primary/5' : 'border-muted-foreground/25'
                      }`}
                    >
                      <input {...getInputProps()} />
                      <div className="space-y-4">
                        <div className="flex justify-center gap-4">
                          <Upload className="h-8 w-8 text-muted-foreground" />
                          <Camera className="h-8 w-8 text-muted-foreground" />
                        </div>
                        <div>
                          <p className="text-lg font-medium">
                            {isDragActive ? 'שחרר כאן...' : 'גרור קבלות או לחץ להעלאה'}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            תמיכה ב-PDF, JPG, PNG עד 10MB
                          </p>
                        </div>
                        <div className="flex justify-center gap-2">
                          <Button variant="outline" size="sm" className="gap-2">
                            <Upload className="h-4 w-4" />
                            העלה קובץ
                          </Button>
                          <Button variant="outline" size="sm" className="gap-2">
                            <Camera className="h-4 w-4" />
                            צלם קבלה
                          </Button>
                        </div>
                      </div>
                    </div>

                    {/* Uploaded Files */}
                    {uploadedFiles.length > 0 && (
                      <div className="space-y-2">
                        <h4 className="font-medium">קבלות שהועלו:</h4>
                        <div className="grid gap-2">
                          {uploadedFiles.map((file, index) => (
                            <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                              <Image className="h-5 w-5 text-muted-foreground" />
                              <span className="flex-1 text-sm">{file.name}</span>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setUploadedFiles(files => files.filter((_, i) => i !== index))}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Expense Form */}
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">כותרת *</label>
                        <Input placeholder="תיאור ההוצאה" />
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">סכום *</label>
                        <Input type="number" placeholder="0.00" />
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">קטגוריה *</label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="בחר קטגוריה" />
                          </SelectTrigger>
                          <SelectContent>
                            {Object.entries(categories).map(([key, category]) => (
                              <SelectItem key={key} value={key}>
                                <div className="flex items-center gap-2">
                                  <span>{category.icon}</span>
                                  {category.label}
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">תאריך *</label>
                        <Input type="date" />
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">ספק</label>
                        <Input placeholder="שם הספק או העסק" />
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">מטבע</label>
                        <Select defaultValue="ILS">
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="ILS">₪ שקל</SelectItem>
                            <SelectItem value="USD">$ דולר</SelectItem>
                            <SelectItem value="EUR">€ יורו</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">הערות</label>
                      <Textarea placeholder="פרטים נוספים על ההוצאה" />
                    </div>

                    <div className="flex justify-end gap-3">
                      <Button variant="outline" onClick={() => setIsNewExpenseOpen(false)}>
                        ביטול
                      </Button>
                      <Button variant="outline">
                        שמירה כטיוטה
                      </Button>
                      <Button>
                        שליחה לאישור
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          }
        />

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card className="cosmic-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">ממתין לאישור</p>
                  <p className="text-2xl font-bold">
                    {formatCurrency(getTotalByStatus('pending'), 'ILS')}
                  </p>
                </div>
                <Clock className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="cosmic-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">אושר החודש</p>
                  <p className="text-2xl font-bold">
                    {formatCurrency(getTotalByStatus('approved'), 'ILS')}
                  </p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="cosmic-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">נדחה</p>
                  <p className="text-2xl font-bold">
                    {formatCurrency(getTotalByStatus('rejected'), 'ILS')}
                  </p>
                </div>
                <XCircle className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="cosmic-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">טיוטות</p>
                  <p className="text-2xl font-bold">
                    {mockExpenses.filter(e => e.status === 'draft').length}
                  </p>
                </div>
                <Edit className="h-8 w-8 text-gray-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Gmail Integration Status */}
        <Card className="cosmic-card border-blue-200 bg-blue-50/50 dark:bg-blue-950/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Mail className="h-8 w-8 text-blue-500" />
                <div>
                  <h3 className="font-semibold">סנכרון Gmail</h3>
                  <p className="text-sm text-muted-foreground">
                    סרוק אוטומטית קבלות מהאימייל שלך
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Badge variant="outline" className="gap-1">
                  <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                  לא מחובר
                </Badge>
                <Button variant="outline" size="sm">
                  חבר חשבון
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Filters and Search */}
        <Card className="cosmic-card">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="חיפוש הוצאות..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="gap-2">
                      <Filter className="h-4 w-4" />
                      סטטוס
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => setStatusFilter('all')}>
                      כל הסטטוסים
                    </DropdownMenuItem>
                    {Object.entries(statusConfig).map(([status, config]) => (
                      <DropdownMenuItem key={status} onClick={() => setStatusFilter(status)}>
                        <config.icon className="ml-2 h-4 w-4" />
                        {config.label}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="gap-2">
                      <Tag className="h-4 w-4" />
                      קטגוריה
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => setCategoryFilter('all')}>
                      כל הקטגוריות
                    </DropdownMenuItem>
                    {Object.entries(categories).map(([category, config]) => (
                      <DropdownMenuItem key={category} onClick={() => setCategoryFilter(category)}>
                        <span className="ml-2">{config.icon}</span>
                        {config.label}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Expenses Table */}
        <Card className="cosmic-card">
          <CardHeader>
            <CardTitle>רשימת הוצאות</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all">
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="all">הכל ({mockExpenses.length})</TabsTrigger>
                <TabsTrigger value="pending">
                  ממתין ({mockExpenses.filter(e => e.status === 'pending').length})
                </TabsTrigger>
                <TabsTrigger value="approved">
                  אושר ({mockExpenses.filter(e => e.status === 'approved').length})
                </TabsTrigger>
                <TabsTrigger value="rejected">
                  נדחה ({mockExpenses.filter(e => e.status === 'rejected').length})
                </TabsTrigger>
                <TabsTrigger value="draft">
                  טיוטות ({mockExpenses.filter(e => e.status === 'draft').length})
                </TabsTrigger>
              </TabsList>

              <TabsContent value="all" className="mt-6">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>הוצאה</TableHead>
                        <TableHead>קטגוריה</TableHead>
                        <TableHead>סכום</TableHead>
                        <TableHead>תאריך</TableHead>
                        <TableHead>סטטוס</TableHead>
                        <TableHead>הוגש על ידי</TableHead>
                        <TableHead className="w-12"></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredExpenses.map((expense) => {
                        const categoryConfig = categories[expense.category as keyof typeof categories];
                        const statusInfo = statusConfig[expense.status as keyof typeof statusConfig];

                        return (
                          <TableRow key={expense.id}>
                            <TableCell>
                              <div className="flex items-center gap-3">
                                <div className="flex items-center gap-2">
                                  {expense.receipt ? (
                                    <Image className="h-5 w-5 text-green-500" />
                                  ) : (
                                    <AlertCircle className="h-5 w-5 text-yellow-500" />
                                  )}
                                </div>
                                <div>
                                  <div className="font-medium">{expense.title}</div>
                                  <div className="text-sm text-muted-foreground">{expense.vendor}</div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline" className="gap-1">
                                <span>{categoryConfig.icon}</span>
                                {categoryConfig.label}
                              </Badge>
                            </TableCell>
                            <TableCell className="font-medium">
                              {formatCurrency(expense.amount, expense.currency)}
                            </TableCell>
                            <TableCell>{formatDate(expense.date)}</TableCell>
                            <TableCell>
                              <Badge variant={statusInfo.variant} className="gap-1">
                                <statusInfo.icon className="h-3 w-3" />
                                {statusInfo.label}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-sm">{expense.submittedBy}</TableCell>
                            <TableCell>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem onClick={() => setSelectedExpense(expense)}>
                                    <Eye className="ml-2 h-4 w-4" />
                                    צפייה
                                  </DropdownMenuItem>
                                  {expense.status === 'draft' && (
                                    <DropdownMenuItem>
                                      <Edit className="ml-2 h-4 w-4" />
                                      עריכה
                                    </DropdownMenuItem>
                                  )}
                                  {expense.status === 'pending' && (
                                    <>
                                      <DropdownMenuItem className="text-green-600">
                                        <Check className="ml-2 h-4 w-4" />
                                        אישור
                                      </DropdownMenuItem>
                                      <DropdownMenuItem className="text-red-600">
                                        <X className="ml-2 h-4 w-4" />
                                        דחייה
                                      </DropdownMenuItem>
                                    </>
                                  )}
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem className="text-destructive">
                                    <Trash2 className="ml-2 h-4 w-4" />
                                    מחיקה
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Expense Details Dialog */}
        <Dialog open={!!selectedExpense} onOpenChange={() => setSelectedExpense(null)}>
          <DialogContent className="max-w-2xl">
            {selectedExpense && (
              <>
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-3">
                    <Receipt className="h-6 w-6" />
                    {selectedExpense.title}
                  </DialogTitle>
                  <DialogDescription>
                    פרטי ההוצאה ומעקב אישורים
                  </DialogDescription>
                </DialogHeader>

                <div className="space-y-6 mt-6">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">סכום</label>
                        <p className="text-2xl font-bold">
                          {formatCurrency(selectedExpense.amount, selectedExpense.currency)}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">קטגוריה</label>
                        <div className="flex items-center gap-2 mt-1">
                          <span>{categories[selectedExpense.category as keyof typeof categories].icon}</span>
                          <span>{categories[selectedExpense.category as keyof typeof categories].label}</span>
                        </div>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">תאריך</label>
                        <p>{formatDate(selectedExpense.date)}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">ספק</label>
                        <p>{selectedExpense.vendor}</p>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">סטטוס</label>
                        <div className="mt-1">
                          <Badge variant={statusConfig[selectedExpense.status as keyof typeof statusConfig].variant}>
                            {statusConfig[selectedExpense.status as keyof typeof statusConfig].label}
                          </Badge>
                        </div>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">הוגש על ידי</label>
                        <p>{selectedExpense.submittedBy}</p>
                        <p className="text-xs text-muted-foreground">
                          {formatDateTime(selectedExpense.submittedAt)}
                        </p>
                      </div>
                      {selectedExpense.approvedBy && (
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">אושר על ידי</label>
                          <p>{selectedExpense.approvedBy}</p>
                          <p className="text-xs text-muted-foreground">
                            {formatDateTime(selectedExpense.approvedAt!)}
                          </p>
                        </div>
                      )}
                      {selectedExpense.rejectedBy && (
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">נדחה על ידי</label>
                          <p>{selectedExpense.rejectedBy}</p>
                          <p className="text-xs text-muted-foreground">
                            {formatDateTime(selectedExpense.rejectedAt!)}
                          </p>
                          {selectedExpense.rejectionReason && (
                            <p className="text-sm text-red-600 mt-1">
                              סיבה: {selectedExpense.rejectionReason}
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  {selectedExpense.description && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">תיאור</label>
                      <p className="mt-1">{selectedExpense.description}</p>
                    </div>
                  )}

                  {selectedExpense.receipt && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">קבלה</label>
                      <div className="mt-2 p-4 border rounded-lg bg-accent/20">
                        <div className="flex items-center gap-3">
                          <Image className="h-8 w-8 text-muted-foreground" />
                          <div>
                            <p className="font-medium">{selectedExpense.receipt}</p>
                            <p className="text-sm text-muted-foreground">לחץ לצפייה</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {selectedExpense.status === 'pending' && (
                    <div className="flex justify-end gap-3 pt-4 border-t">
                      <Button variant="outline" className="gap-2 text-red-600 border-red-200 hover:bg-red-50">
                        <X className="h-4 w-4" />
                        דחה
                      </Button>
                      <Button className="gap-2">
                        <Check className="h-4 w-4" />
                        אשר
                      </Button>
                    </div>
                  )}
                </div>
              </>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </AppShell>
  );
};

export default Expenses;
