import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AppShell from '@/components/layout/AppShell';
import PageHeader from '@/components/layout/PageHeader';
import {
  FileText,
  Receipt,
  Search,
  Filter,
  Download,
  MoreH<PERSON>zontal,
  Plus,
  Eye,
  Edit,
  Trash2,
  Send,
  CheckCircle,
  Clock,
  AlertCircle,
  Calendar
} from 'lucide-react';

// Mock data for documents
const mockDocuments = [
  {
    id: '2024-001',
    type: 'tax-invoice',
    title: 'חשבונית מס #2024-001',
    client: 'ABC בע"מ',
    amount: 2500,
    currency: 'ILS',
    status: 'sent',
    date: '2024-12-01',
    dueDate: '2024-12-31',
    description: 'שירותי ייעוץ טכנולוגי',
  },
  {
    id: '2024-002',
    type: 'invoice-receipt',
    title: 'חשבונית מס-קבלה #2024-002',
    client: 'XYZ חברה',
    amount: 1800,
    currency: 'ILS',
    status: 'paid',
    date: '2024-11-28',
    dueDate: '2024-12-28',
    description: 'פיתוח אתר אינטרנט',
  },
  {
    id: '2024-003',
    type: 'receipt',
    title: 'קבלה #2024-003',
    client: 'DEF עסקים',
    amount: 950,
    currency: 'ILS',
    status: 'draft',
    date: '2024-11-25',
    dueDate: '2024-12-25',
    description: 'תחזוקת מערכות',
  },
  {
    id: '2024-004',
    type: 'quote',
    title: 'הצעת מחיר #2024-004',
    client: 'GHI טכנולוגיות',
    amount: 5200,
    currency: 'ILS',
    status: 'pending',
    date: '2024-11-20',
    dueDate: '2024-12-20',
    description: 'פרויקט אוטומציה',
  },
];

const Documents: React.FC = () => {
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');

  const documentTypes = {
    'tax-invoice': { label: 'חשבונית מס', icon: FileText, color: 'bg-blue-500' },
    'invoice-receipt': { label: 'חשבונית מס-קבלה', icon: Receipt, color: 'bg-green-500' },
    'receipt': { label: 'קבלה', icon: Receipt, color: 'bg-purple-500' },
    'quote': { label: 'הצעת מחיר', icon: FileText, color: 'bg-orange-500' },
  };

  const statusConfig = {
    draft: { label: 'טיוטה', variant: 'secondary' as const, icon: Edit },
    sent: { label: 'נשלח', variant: 'default' as const, icon: Send },
    paid: { label: 'שולם', variant: 'default' as const, icon: CheckCircle },
    pending: { label: 'ממתין', variant: 'secondary' as const, icon: Clock },
    overdue: { label: 'פג תוקף', variant: 'destructive' as const, icon: AlertCircle },
  };

  const filteredDocuments = mockDocuments.filter(doc => {
    const matchesSearch = doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         doc.client.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         doc.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesTab = activeTab === 'all' || doc.type === activeTab;
    const matchesStatus = statusFilter === 'all' || doc.status === statusFilter;
    
    return matchesSearch && matchesTab && matchesStatus;
  });

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedDocuments(filteredDocuments.map(doc => doc.id));
    } else {
      setSelectedDocuments([]);
    }
  };

  const handleSelectDocument = (documentId: string, checked: boolean) => {
    if (checked) {
      setSelectedDocuments(prev => [...prev, documentId]);
    } else {
      setSelectedDocuments(prev => prev.filter(id => id !== documentId));
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('he-IL', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('he-IL');
  };

  return (
    <AppShell>
      <div className="space-y-6">
        <PageHeader
          title="מסמכים"
          subtitle="נהל את כל החשבוניות, הקבלות והצעות המחיר שלך"
          breadcrumbs={[
            { label: 'לוח בקרה', href: '/dashboard' },
            { label: 'מסמכים' }
          ]}
          actions={
            <div className="flex items-center gap-3">
              <Button variant="outline" className="gap-2">
                <Download className="h-4 w-4" />
                ייצוא
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button className="gap-2">
                    <Plus className="h-4 w-4" />
                    מסמך חדש
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>סוג מסמך</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {Object.entries(documentTypes).map(([type, config]) => (
                    <DropdownMenuItem key={type} asChild>
                      <Link to={`/app/documents/new?type=${type}`}>
                        <config.icon className="ml-2 h-4 w-4" />
                        {config.label}
                      </Link>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          }
        />

        {/* Filters and Search */}
        <Card className="cosmic-card">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="חיפוש מסמכים..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="gap-2">
                      <Filter className="h-4 w-4" />
                      סטטוס
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem onClick={() => setStatusFilter('all')}>
                      כל הסטטוסים
                    </DropdownMenuItem>
                    {Object.entries(statusConfig).map(([status, config]) => (
                      <DropdownMenuItem key={status} onClick={() => setStatusFilter(status)}>
                        <config.icon className="ml-2 h-4 w-4" />
                        {config.label}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Document Tabs and Table */}
        <Card className="cosmic-card">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>רשימת מסמכים</CardTitle>
              {selectedDocuments.length > 0 && (
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">
                    {selectedDocuments.length} נבחרו
                  </span>
                  <Button variant="outline" size="sm">
                    פעולות קבוצתיות
                  </Button>
                </div>
              )}
            </div>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="all">הכל ({mockDocuments.length})</TabsTrigger>
                <TabsTrigger value="tax-invoice">
                  חשבוניות מס ({mockDocuments.filter(d => d.type === 'tax-invoice').length})
                </TabsTrigger>
                <TabsTrigger value="invoice-receipt">
                  מס-קבלה ({mockDocuments.filter(d => d.type === 'invoice-receipt').length})
                </TabsTrigger>
                <TabsTrigger value="receipt">
                  קבלות ({mockDocuments.filter(d => d.type === 'receipt').length})
                </TabsTrigger>
                <TabsTrigger value="quote">
                  הצעות מחיר ({mockDocuments.filter(d => d.type === 'quote').length})
                </TabsTrigger>
              </TabsList>

              <TabsContent value={activeTab} className="mt-6">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">
                          <Checkbox
                            checked={selectedDocuments.length === filteredDocuments.length && filteredDocuments.length > 0}
                            onCheckedChange={handleSelectAll}
                          />
                        </TableHead>
                        <TableHead>מסמך</TableHead>
                        <TableHead>לקוח</TableHead>
                        <TableHead>סכום</TableHead>
                        <TableHead>סטטוס</TableHead>
                        <TableHead>תאריך</TableHead>
                        <TableHead>תאריך יעד</TableHead>
                        <TableHead className="w-12"></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredDocuments.map((document) => {
                        const typeConfig = documentTypes[document.type as keyof typeof documentTypes];
                        const statusInfo = statusConfig[document.status as keyof typeof statusConfig];
                        
                        return (
                          <TableRow key={document.id}>
                            <TableCell>
                              <Checkbox
                                checked={selectedDocuments.includes(document.id)}
                                onCheckedChange={(checked) => handleSelectDocument(document.id, checked as boolean)}
                              />
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-3">
                                <div className={`w-8 h-8 rounded-lg ${typeConfig.color} flex items-center justify-center`}>
                                  <typeConfig.icon className="h-4 w-4 text-white" />
                                </div>
                                <div>
                                  <div className="font-medium">{document.title}</div>
                                  <div className="text-sm text-muted-foreground">{document.description}</div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell className="font-medium">{document.client}</TableCell>
                            <TableCell className="font-medium">
                              {formatCurrency(document.amount, document.currency)}
                            </TableCell>
                            <TableCell>
                              <Badge variant={statusInfo.variant} className="gap-1">
                                <statusInfo.icon className="h-3 w-3" />
                                {statusInfo.label}
                              </Badge>
                            </TableCell>
                            <TableCell>{formatDate(document.date)}</TableCell>
                            <TableCell>{formatDate(document.dueDate)}</TableCell>
                            <TableCell>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem>
                                    <Eye className="ml-2 h-4 w-4" />
                                    צפייה
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Edit className="ml-2 h-4 w-4" />
                                    עריכה
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    <Download className="ml-2 h-4 w-4" />
                                    הורדה
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem className="text-destructive">
                                    <Trash2 className="ml-2 h-4 w-4" />
                                    מחיקה
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </AppShell>
  );
};

export default Documents;
