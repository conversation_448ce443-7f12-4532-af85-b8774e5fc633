-- Fix user_profiles RLS policy to allow profile creation during signup
-- Drop the existing restrictive INSERT policy
DROP POLICY IF EXISTS "Users can insert their own profile" ON user_profiles;

-- Create a more permissive INSERT policy that allows:
-- 1. Users to insert their own profile (when auth.uid() is available)
-- 2. System/trigger to insert profiles during signup (when auth.uid() might not be set yet)
CREATE POLICY "Allow profile creation during signup" ON user_profiles
    FOR INSERT WITH CHECK (
        -- Allow if the user is authenticated and inserting their own profile
        (auth.uid() IS NOT NULL AND id = auth.uid()) OR
        -- Allow if this is a system operation (like during signup trigger)
        (auth.uid() IS NULL) OR
        -- Allow if the ID matches the current user (fallback case)
        (id = auth.uid())
    );
