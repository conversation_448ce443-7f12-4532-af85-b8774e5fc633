export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: <PERSON><PERSON> | undefined }
  | Json[]

export type UserRole = 'owner' | 'admin' | 'manager' | 'user'
export type SubscriptionStatus = 'active' | 'inactive' | 'cancelled' | 'past_due'
export type SubscriptionPlan = 'free' | 'advanced' | 'enterprise'

export type Database = {
  public: {
    Tables: {
      organizations: {
        Row: {
          id: string
          name: string
          subscription_plan: SubscriptionPlan
          subscription_status: SubscriptionStatus
          max_users: number
          max_invoices: number
          settings: <PERSON><PERSON>
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          subscription_plan?: SubscriptionPlan
          subscription_status?: SubscriptionStatus
          max_users?: number
          max_invoices?: number
          settings?: <PERSON><PERSON>
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          subscription_plan?: SubscriptionPlan
          subscription_status?: SubscriptionStatus
          max_users?: number
          max_invoices?: number
          settings?: Json
          created_at?: string
          updated_at?: string
        }
      }
      user_profiles: {
        Row: {
          id: string
          email: string
          first_name: string | null
          last_name: string | null
          avatar_url: string | null
          phone: string | null
          timezone: string
          language: string
          preferences: Json
          company_name: string | null
          annual_revenue: string | null
          accounting_services: boolean
          business_loan: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          first_name?: string | null
          last_name?: string | null
          avatar_url?: string | null
          phone?: string | null
          timezone?: string
          language?: string
          preferences?: Json
          company_name?: string | null
          annual_revenue?: string | null
          accounting_services?: boolean
          business_loan?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          first_name?: string | null
          last_name?: string | null
          avatar_url?: string | null
          phone?: string | null
          timezone?: string
          language?: string
          preferences?: Json
          company_name?: string | null
          annual_revenue?: string | null
          accounting_services?: boolean
          business_loan?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      organization_members: {
        Row: {
          id: string
          organization_id: string
          user_id: string
          role: UserRole
          invited_by: string | null
          invited_at: string | null
          joined_at: string
          is_active: boolean
        }
        Insert: {
          id?: string
          organization_id: string
          user_id: string
          role?: UserRole
          invited_by?: string | null
          invited_at?: string | null
          joined_at?: string
          is_active?: boolean
        }
        Update: {
          id?: string
          organization_id?: string
          user_id?: string
          role?: UserRole
          invited_by?: string | null
          invited_at?: string | null
          joined_at?: string
          is_active?: boolean
        }
      }
      invoices: {
        Row: {
          id: string
          organization_id: string
          created_by: string
          invoice_number: string
          client_name: string
          client_email: string | null
          client_address: string | null
          amount: number
          currency: string
          tax_amount: number
          total_amount: number
          due_date: string | null
          issue_date: string
          status: string
          description: string | null
          line_items: Json
          metadata: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          created_by: string
          invoice_number: string
          client_name: string
          client_email?: string | null
          client_address?: string | null
          amount: number
          currency?: string
          tax_amount?: number
          total_amount: number
          due_date?: string | null
          issue_date?: string
          status?: string
          description?: string | null
          line_items?: Json
          metadata?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          created_by?: string
          invoice_number?: string
          client_name?: string
          client_email?: string | null
          client_address?: string | null
          amount?: number
          currency?: string
          tax_amount?: number
          total_amount?: number
          due_date?: string | null
          issue_date?: string
          status?: string
          description?: string | null
          line_items?: Json
          metadata?: Json
          created_at?: string
          updated_at?: string
        }
      }
      expenses: {
        Row: {
          id: string
          organization_id: string
          created_by: string
          amount: number
          currency: string
          category: string | null
          description: string | null
          vendor: string | null
          receipt_url: string | null
          expense_date: string
          is_reimbursable: boolean
          status: string
          metadata: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          created_by: string
          amount: number
          currency?: string
          category?: string | null
          description?: string | null
          vendor?: string | null
          receipt_url?: string | null
          expense_date?: string
          is_reimbursable?: boolean
          status?: string
          metadata?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          created_by?: string
          amount?: number
          currency?: string
          category?: string | null
          description?: string | null
          vendor?: string | null
          receipt_url?: string | null
          expense_date?: string
          is_reimbursable?: boolean
          status?: string
          metadata?: Json
          created_at?: string
          updated_at?: string
        }
      }
      ai_chat_sessions: {
        Row: {
          id: string
          organization_id: string
          user_id: string
          phone_number: string | null
          session_data: Json
          last_message_at: string
          is_active: boolean
          created_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          user_id: string
          phone_number?: string | null
          session_data?: Json
          last_message_at?: string
          is_active?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          user_id?: string
          phone_number?: string | null
          session_data?: Json
          last_message_at?: string
          is_active?: boolean
          created_at?: string
        }
      }
      ai_chat_messages: {
        Row: {
          id: string
          session_id: string
          message_type: string
          content: string | null
          file_url: string | null
          is_from_user: boolean
          processed_data: Json
          created_at: string
        }
        Insert: {
          id?: string
          session_id: string
          message_type: string
          content?: string | null
          file_url?: string | null
          is_from_user: boolean
          processed_data?: Json
          created_at?: string
        }
        Update: {
          id?: string
          session_id?: string
          message_type?: string
          content?: string | null
          file_url?: string | null
          is_from_user?: boolean
          processed_data?: Json
          created_at?: string
        }
      }
      email_integrations: {
        Row: {
          id: string
          organization_id: string
          user_id: string
          email_address: string
          provider: string | null
          access_token_encrypted: string | null
          refresh_token_encrypted: string | null
          is_active: boolean
          last_sync_at: string | null
          created_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          user_id: string
          email_address: string
          provider?: string | null
          access_token_encrypted?: string | null
          refresh_token_encrypted?: string | null
          is_active?: boolean
          last_sync_at?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          user_id?: string
          email_address?: string
          provider?: string | null
          access_token_encrypted?: string | null
          refresh_token_encrypted?: string | null
          is_active?: boolean
          last_sync_at?: string | null
          created_at?: string
        }
      }
      reports: {
        Row: {
          id: string
          organization_id: string
          created_by: string
          report_type: string
          title: string
          parameters: Json
          data: Json
          scheduled: boolean
          schedule_config: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          created_by: string
          report_type: string
          title: string
          parameters?: Json
          data?: Json
          scheduled?: boolean
          schedule_config?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          created_by?: string
          report_type?: string
          title?: string
          parameters?: Json
          data?: Json
          scheduled?: boolean
          schedule_config?: Json
          created_at?: string
          updated_at?: string
        }
      }
      audit_logs: {
        Row: {
          id: string
          organization_id: string | null
          user_id: string | null
          action: string
          resource_type: string | null
          resource_id: string | null
          details: Json
          ip_address: string | null
          user_agent: string | null
          created_at: string
        }
        Insert: {
          id?: string
          organization_id?: string | null
          user_id?: string | null
          action: string
          resource_type?: string | null
          resource_id?: string | null
          details?: Json
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          organization_id?: string | null
          user_id?: string | null
          action?: string
          resource_type?: string | null
          resource_id?: string | null
          details?: Json
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      user_role: UserRole
      subscription_status: SubscriptionStatus
      subscription_plan: SubscriptionPlan
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      user_role: ['owner', 'admin', 'manager', 'user'] as const,
      subscription_status: ['active', 'inactive', 'cancelled', 'past_due'] as const,
      subscription_plan: ['free', 'advanced', 'enterprise'] as const,
    },
  },
} as const
