import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import AppShell from '@/components/layout/AppShell';
import PageHeader from '@/components/layout/PageHeader';
import {
  Settings as SettingsIcon,
  Building2,
  FileText,
  Mail,
  Bell,
  Smartphone,
  Globe,
  Shield,
  Palette,
  Save,
  Upload,
  Download,
  Trash2,
  Plus,
  <PERSON>,
  Check,
  X,
  MessageSquare,
  Zap,
  CreditCard,
  Users,
  Lock
} from 'lucide-react';

const Settings: React.FC = () => {
  const [activeTab, setActiveTab] = useState('business');
  const [isEditing, setIsEditing] = useState(false);

  // Mock business information
  const [businessInfo, setBusinessInfo] = useState({
    name: 'חברת הטכנולוגיה המתקדמת בע"מ',
    taxId: '*********',
    address: 'רחוב הרצל 123, תל אביב',
    phone: '03-1234567',
    email: '<EMAIL>',
    website: 'www.company.co.il',
    logo: null,
    bankAccount: '*************',
    bankName: 'בנק הפועלים',
    vatNumber: '*********',
  });

  const [integrationSettings, setIntegrationSettings] = useState({
    gmail: { connected: false, email: '' },
    whatsapp: { connected: true, phone: '+************' },
    accounting: { connected: false, system: '' },
    banking: { connected: false, bank: '' },
  });

  const [notificationSettings, setNotificationSettings] = useState({
    email: {
      newDocument: true,
      paymentReceived: true,
      documentOverdue: true,
      monthlyReport: false,
    },
    whatsapp: {
      newDocument: false,
      paymentReceived: true,
      documentOverdue: true,
      monthlyReport: false,
    },
    push: {
      newDocument: true,
      paymentReceived: true,
      documentOverdue: true,
      monthlyReport: false,
    },
  });

  const [documentTemplates, setDocumentTemplates] = useState([
    {
      id: '1',
      name: 'תבנית חשבונית מס סטנדרטית',
      type: 'tax-invoice',
      isDefault: true,
      lastModified: '2024-11-15',
    },
    {
      id: '2',
      name: 'תבנית הצעת מחיר',
      type: 'quote',
      isDefault: true,
      lastModified: '2024-11-10',
    },
    {
      id: '3',
      name: 'תבנית קבלה פשוטה',
      type: 'receipt',
      isDefault: false,
      lastModified: '2024-11-05',
    },
  ]);

  const handleSaveBusinessInfo = () => {
    setIsEditing(false);
    // Here you would typically save to backend
    console.log('Saving business info:', businessInfo);
  };

  const handleConnectIntegration = (service: string) => {
    // Here you would typically handle OAuth or API connection
    console.log('Connecting to:', service);
  };

  const handleDisconnectIntegration = (service: string) => {
    setIntegrationSettings(prev => ({
      ...prev,
      [service]: { ...prev[service as keyof typeof prev], connected: false }
    }));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('he-IL');
  };

  return (
    <AppShell>
      <div className="space-y-6">
        <PageHeader
          title="הגדרות"
          subtitle="נהל את הגדרות החשבון, התבניות והאינטגרציות"
          breadcrumbs={[
            { label: 'לוח בקרה', href: '/dashboard' },
            { label: 'הגדרות' }
          ]}
          actions={
            <Button className="gap-2" onClick={handleSaveBusinessInfo}>
              <Save className="h-4 w-4" />
              שמור שינויים
            </Button>
          }
        />

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="business" className="gap-2">
              <Building2 className="h-4 w-4" />
              פרטי עסק
            </TabsTrigger>
            <TabsTrigger value="templates" className="gap-2">
              <FileText className="h-4 w-4" />
              תבניות
            </TabsTrigger>
            <TabsTrigger value="integrations" className="gap-2">
              <Zap className="h-4 w-4" />
              אינטגרציות
            </TabsTrigger>
            <TabsTrigger value="notifications" className="gap-2">
              <Bell className="h-4 w-4" />
              התראות
            </TabsTrigger>
            <TabsTrigger value="security" className="gap-2">
              <Shield className="h-4 w-4" />
              אבטחה
            </TabsTrigger>
          </TabsList>

          {/* Business Information Tab */}
          <TabsContent value="business" className="space-y-6">
            <Card className="cosmic-card">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Building2 className="h-5 w-5" />
                    פרטי החברה
                  </CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsEditing(!isEditing)}
                  >
                    <Edit className="h-4 w-4 ml-2" />
                    {isEditing ? 'ביטול' : 'עריכה'}
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="company-name">שם החברה *</Label>
                    <Input
                      id="company-name"
                      value={businessInfo.name}
                      onChange={(e) => setBusinessInfo(prev => ({ ...prev, name: e.target.value }))}
                      disabled={!isEditing}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="tax-id">מספר עוסק מורשה *</Label>
                    <Input
                      id="tax-id"
                      value={businessInfo.taxId}
                      onChange={(e) => setBusinessInfo(prev => ({ ...prev, taxId: e.target.value }))}
                      disabled={!isEditing}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address">כתובת *</Label>
                  <Textarea
                    id="address"
                    value={businessInfo.address}
                    onChange={(e) => setBusinessInfo(prev => ({ ...prev, address: e.target.value }))}
                    disabled={!isEditing}
                    className="min-h-[80px]"
                  />
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="phone">טלפון</Label>
                    <Input
                      id="phone"
                      value={businessInfo.phone}
                      onChange={(e) => setBusinessInfo(prev => ({ ...prev, phone: e.target.value }))}
                      disabled={!isEditing}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">אימייל</Label>
                    <Input
                      id="email"
                      type="email"
                      value={businessInfo.email}
                      onChange={(e) => setBusinessInfo(prev => ({ ...prev, email: e.target.value }))}
                      disabled={!isEditing}
                    />
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="website">אתר אינטרנט</Label>
                    <Input
                      id="website"
                      value={businessInfo.website}
                      onChange={(e) => setBusinessInfo(prev => ({ ...prev, website: e.target.value }))}
                      disabled={!isEditing}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="vat-number">מספר מע"מ</Label>
                    <Input
                      id="vat-number"
                      value={businessInfo.vatNumber}
                      onChange={(e) => setBusinessInfo(prev => ({ ...prev, vatNumber: e.target.value }))}
                      disabled={!isEditing}
                    />
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">פרטי בנק</h3>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="bank-name">שם הבנק</Label>
                      <Input
                        id="bank-name"
                        value={businessInfo.bankName}
                        onChange={(e) => setBusinessInfo(prev => ({ ...prev, bankName: e.target.value }))}
                        disabled={!isEditing}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="bank-account">מספר חשבון</Label>
                      <Input
                        id="bank-account"
                        value={businessInfo.bankAccount}
                        onChange={(e) => setBusinessInfo(prev => ({ ...prev, bankAccount: e.target.value }))}
                        disabled={!isEditing}
                      />
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">לוגו החברה</h3>
                  <div className="flex items-center gap-4">
                    <div className="w-24 h-24 border-2 border-dashed border-muted-foreground/25 rounded-lg flex items-center justify-center">
                      <Building2 className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <div className="space-y-2">
                      <Button variant="outline" size="sm" disabled={!isEditing}>
                        <Upload className="h-4 w-4 ml-2" />
                        העלה לוגו
                      </Button>
                      <p className="text-sm text-muted-foreground">
                        PNG, JPG עד 2MB
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Document Templates Tab */}
          <TabsContent value="templates" className="space-y-6">
            <Card className="cosmic-card">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    תבניות מסמכים
                  </CardTitle>
                  <Button className="gap-2">
                    <Plus className="h-4 w-4" />
                    תבנית חדשה
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {documentTemplates.map((template) => (
                    <div key={template.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <FileText className="h-8 w-8 text-muted-foreground" />
                        <div>
                          <div className="font-medium">{template.name}</div>
                          <div className="text-sm text-muted-foreground">
                            עודכן לאחרונה: {formatDate(template.lastModified)}
                          </div>
                        </div>
                        {template.isDefault && (
                          <Badge variant="default">ברירת מחדל</Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                        {!template.isDefault && (
                          <Button variant="outline" size="sm" className="text-destructive">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Integrations Tab */}
          <TabsContent value="integrations" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              {/* Gmail Integration */}
              <Card className="cosmic-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Mail className="h-5 w-5" />
                    Gmail
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">סנכרון אוטומטי של קבלות</p>
                      <p className="text-sm text-muted-foreground">
                        זיהוי וייבוא אוטומטי של קבלות מהאימייל
                      </p>
                    </div>
                    <Badge variant={integrationSettings.gmail.connected ? 'default' : 'secondary'}>
                      {integrationSettings.gmail.connected ? 'מחובר' : 'לא מחובר'}
                    </Badge>
                  </div>
                  {integrationSettings.gmail.connected ? (
                    <div className="space-y-2">
                      <p className="text-sm">חשבון: {integrationSettings.gmail.email}</p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDisconnectIntegration('gmail')}
                      >
                        ניתוק
                      </Button>
                    </div>
                  ) : (
                    <Button
                      variant="outline"
                      onClick={() => handleConnectIntegration('gmail')}
                    >
                      חיבור לGmail
                    </Button>
                  )}
                </CardContent>
              </Card>

              {/* WhatsApp Integration */}
              <Card className="cosmic-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageSquare className="h-5 w-5" />
                    WhatsApp Business
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">עוזר AI לניהול הוצאות</p>
                      <p className="text-sm text-muted-foreground">
                        קבלת הוצאות דרך הודעות WhatsApp
                      </p>
                    </div>
                    <Badge variant={integrationSettings.whatsapp.connected ? 'default' : 'secondary'}>
                      {integrationSettings.whatsapp.connected ? 'מחובר' : 'לא מחובר'}
                    </Badge>
                  </div>
                  {integrationSettings.whatsapp.connected ? (
                    <div className="space-y-2">
                      <p className="text-sm">מספר: {integrationSettings.whatsapp.phone}</p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDisconnectIntegration('whatsapp')}
                      >
                        ניתוק
                      </Button>
                    </div>
                  ) : (
                    <Button
                      variant="outline"
                      onClick={() => handleConnectIntegration('whatsapp')}
                    >
                      חיבור לWhatsApp
                    </Button>
                  )}
                </CardContent>
              </Card>

              {/* Accounting Software Integration */}
              <Card className="cosmic-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5" />
                    תוכנת הנהלת חשבונות
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">סנכרון עם רואה חשבון</p>
                      <p className="text-sm text-muted-foreground">
                        העברת נתונים לתוכנת הנהלת חשבונות
                      </p>
                    </div>
                    <Badge variant={integrationSettings.accounting.connected ? 'default' : 'secondary'}>
                      {integrationSettings.accounting.connected ? 'מחובר' : 'לא מחובר'}
                    </Badge>
                  </div>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="בחר תוכנה" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="priority">פריוריטי</SelectItem>
                      <SelectItem value="sage">Sage</SelectItem>
                      <SelectItem value="quickbooks">QuickBooks</SelectItem>
                      <SelectItem value="other">אחר</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline" disabled>
                    חיבור (בקרוב)
                  </Button>
                </CardContent>
              </Card>

              {/* Banking Integration */}
              <Card className="cosmic-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building2 className="h-5 w-5" />
                    חיבור בנקאי
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">סנכרון תנועות בנק</p>
                      <p className="text-sm text-muted-foreground">
                        ייבוא אוטומטי של תנועות חשבון
                      </p>
                    </div>
                    <Badge variant={integrationSettings.banking.connected ? 'default' : 'secondary'}>
                      {integrationSettings.banking.connected ? 'מחובר' : 'לא מחובר'}
                    </Badge>
                  </div>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="בחר בנק" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="hapoalim">בנק הפועלים</SelectItem>
                      <SelectItem value="leumi">בנק לאומי</SelectItem>
                      <SelectItem value="discount">בנק דיסקונט</SelectItem>
                      <SelectItem value="mizrahi">בנק מזרחי</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline" disabled>
                    חיבור (בקרוב)
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Notifications Tab */}
          <TabsContent value="notifications" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-3">
              {/* Email Notifications */}
              <Card className="cosmic-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Mail className="h-5 w-5" />
                    התראות אימייל
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="email-new-doc">מסמך חדש נוצר</Label>
                    <Switch
                      id="email-new-doc"
                      checked={notificationSettings.email.newDocument}
                      onCheckedChange={(checked) =>
                        setNotificationSettings(prev => ({
                          ...prev,
                          email: { ...prev.email, newDocument: checked }
                        }))
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="email-payment">תשלום התקבל</Label>
                    <Switch
                      id="email-payment"
                      checked={notificationSettings.email.paymentReceived}
                      onCheckedChange={(checked) =>
                        setNotificationSettings(prev => ({
                          ...prev,
                          email: { ...prev.email, paymentReceived: checked }
                        }))
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="email-overdue">מסמך פג תוקף</Label>
                    <Switch
                      id="email-overdue"
                      checked={notificationSettings.email.documentOverdue}
                      onCheckedChange={(checked) =>
                        setNotificationSettings(prev => ({
                          ...prev,
                          email: { ...prev.email, documentOverdue: checked }
                        }))
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="email-monthly">דוח חודשי</Label>
                    <Switch
                      id="email-monthly"
                      checked={notificationSettings.email.monthlyReport}
                      onCheckedChange={(checked) =>
                        setNotificationSettings(prev => ({
                          ...prev,
                          email: { ...prev.email, monthlyReport: checked }
                        }))
                      }
                    />
                  </div>
                </CardContent>
              </Card>

              {/* WhatsApp Notifications */}
              <Card className="cosmic-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageSquare className="h-5 w-5" />
                    התראות WhatsApp
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="whatsapp-new-doc">מסמך חדש נוצר</Label>
                    <Switch
                      id="whatsapp-new-doc"
                      checked={notificationSettings.whatsapp.newDocument}
                      onCheckedChange={(checked) =>
                        setNotificationSettings(prev => ({
                          ...prev,
                          whatsapp: { ...prev.whatsapp, newDocument: checked }
                        }))
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="whatsapp-payment">תשלום התקבל</Label>
                    <Switch
                      id="whatsapp-payment"
                      checked={notificationSettings.whatsapp.paymentReceived}
                      onCheckedChange={(checked) =>
                        setNotificationSettings(prev => ({
                          ...prev,
                          whatsapp: { ...prev.whatsapp, paymentReceived: checked }
                        }))
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="whatsapp-overdue">מסמך פג תוקף</Label>
                    <Switch
                      id="whatsapp-overdue"
                      checked={notificationSettings.whatsapp.documentOverdue}
                      onCheckedChange={(checked) =>
                        setNotificationSettings(prev => ({
                          ...prev,
                          whatsapp: { ...prev.whatsapp, documentOverdue: checked }
                        }))
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="whatsapp-monthly">דוח חודשי</Label>
                    <Switch
                      id="whatsapp-monthly"
                      checked={notificationSettings.whatsapp.monthlyReport}
                      onCheckedChange={(checked) =>
                        setNotificationSettings(prev => ({
                          ...prev,
                          whatsapp: { ...prev.whatsapp, monthlyReport: checked }
                        }))
                      }
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Push Notifications */}
              <Card className="cosmic-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Smartphone className="h-5 w-5" />
                    התראות דחיפה
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="push-new-doc">מסמך חדש נוצר</Label>
                    <Switch
                      id="push-new-doc"
                      checked={notificationSettings.push.newDocument}
                      onCheckedChange={(checked) =>
                        setNotificationSettings(prev => ({
                          ...prev,
                          push: { ...prev.push, newDocument: checked }
                        }))
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="push-payment">תשלום התקבל</Label>
                    <Switch
                      id="push-payment"
                      checked={notificationSettings.push.paymentReceived}
                      onCheckedChange={(checked) =>
                        setNotificationSettings(prev => ({
                          ...prev,
                          push: { ...prev.push, paymentReceived: checked }
                        }))
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="push-overdue">מסמך פג תוקף</Label>
                    <Switch
                      id="push-overdue"
                      checked={notificationSettings.push.documentOverdue}
                      onCheckedChange={(checked) =>
                        setNotificationSettings(prev => ({
                          ...prev,
                          push: { ...prev.push, documentOverdue: checked }
                        }))
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="push-monthly">דוח חודשי</Label>
                    <Switch
                      id="push-monthly"
                      checked={notificationSettings.push.monthlyReport}
                      onCheckedChange={(checked) =>
                        setNotificationSettings(prev => ({
                          ...prev,
                          push: { ...prev.push, monthlyReport: checked }
                        }))
                      }
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Security Tab */}
          <TabsContent value="security" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              {/* Account Security */}
              <Card className="cosmic-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Lock className="h-5 w-5" />
                    אבטחת חשבון
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label>שינוי סיסמה</Label>
                    <div className="space-y-2">
                      <Input type="password" placeholder="סיסמה נוכחית" />
                      <Input type="password" placeholder="סיסמה חדשה" />
                      <Input type="password" placeholder="אימות סיסמה חדשה" />
                    </div>
                    <Button variant="outline" size="sm">
                      עדכן סיסמה
                    </Button>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <Label>אימות דו-שלבי</Label>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm">הגנה נוספת לחשבון שלך</p>
                        <p className="text-xs text-muted-foreground">
                          דורש קוד מהטלפון בכל התחברות
                        </p>
                      </div>
                      <Switch />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Data & Privacy */}
              <Card className="cosmic-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    נתונים ופרטיות
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium">ייצוא נתונים</h4>
                      <p className="text-sm text-muted-foreground mb-2">
                        הורד עותק של כל הנתונים שלך
                      </p>
                      <Button variant="outline" size="sm">
                        <Download className="h-4 w-4 ml-2" />
                        ייצוא נתונים
                      </Button>
                    </div>

                    <Separator />

                    <div>
                      <h4 className="font-medium">מחיקת חשבון</h4>
                      <p className="text-sm text-muted-foreground mb-2">
                        מחק לצמיתות את החשבון וכל הנתונים
                      </p>
                      <Button variant="destructive" size="sm">
                        <Trash2 className="h-4 w-4 ml-2" />
                        מחק חשבון
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Session Management */}
              <Card className="cosmic-card md:col-span-2">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    ניהול הפעלות
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <div>
                          <p className="font-medium">המכשיר הנוכחי</p>
                          <p className="text-sm text-muted-foreground">
                            Chrome על Windows • תל אביב, ישראל
                          </p>
                        </div>
                      </div>
                      <Badge variant="default">פעיל כעת</Badge>
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
                        <div>
                          <p className="font-medium">iPhone</p>
                          <p className="text-sm text-muted-foreground">
                            Safari • לפני 2 שעות
                          </p>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        נתק
                      </Button>
                    </div>

                    <Button variant="outline" className="w-full">
                      נתק מכל המכשירים
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AppShell>
  );
};

export default Settings;
