import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = 'https://hstufgdxzktuxglpalcb.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhzdHVmZ2R4emt0dXhnbHBhbGNiIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNDk2NzI5NywiZXhwIjoyMDUwNTQzMjk3fQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function clearAllTables() {
  console.log('🧹 Starting database cleanup...');
  
  try {
    // Clear tables in order (respecting foreign key constraints)
    const tables = [
      'audit_logs',
      'reports', 
      'email_integrations',
      'ai_chat_messages',
      'ai_chat_sessions',
      'expenses',
      'invoices',
      'organization_members',
      'user_profiles',
      'organizations'
    ];

    for (const table of tables) {
      console.log(`🗑️ Clearing ${table}...`);
      const { error } = await supabase
        .from(table)
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all rows
      
      if (error) {
        console.error(`❌ Error clearing ${table}:`, error);
      } else {
        console.log(`✅ Cleared ${table}`);
      }
    }

    // Also clear auth users (this will cascade to user_profiles)
    console.log('🗑️ Clearing auth users...');
    const { data: users, error: getUsersError } = await supabase.auth.admin.listUsers();
    
    if (getUsersError) {
      console.error('❌ Error getting users:', getUsersError);
    } else {
      for (const user of users.users) {
        const { error: deleteError } = await supabase.auth.admin.deleteUser(user.id);
        if (deleteError) {
          console.error(`❌ Error deleting user ${user.email}:`, deleteError);
        } else {
          console.log(`✅ Deleted user ${user.email}`);
        }
      }
    }

    console.log('🎉 Database cleanup completed!');
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  }
}

clearAllTables();
