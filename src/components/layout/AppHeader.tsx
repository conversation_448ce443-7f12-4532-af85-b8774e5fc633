import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import {
  Menu,
  Search,
  Bell,
  Sun,
  Moon,
  User,
  Settings,
  LogOut,
  Plus,
  FileText,
  Receipt
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from 'next-themes';
import { cn } from '@/lib/utils';

interface AppHeaderProps {
  onMenuClick?: () => void;
  showMenuButton?: boolean;
  title?: string;
  subtitle?: string;
}

const AppHeader: React.FC<AppHeaderProps> = ({
  onMenuClick,
  showMenuButton = false,
  title,
  subtitle
}) => {
  const { userProfile, signOut } = useAuth();
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    setMounted(true);
  }, []);

  const toggleTheme = () => {
    if (!mounted) return;
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const isDarkMode = mounted && theme === 'dark';
  const isLightMode = mounted && theme === 'light';

  const userInitials = userProfile?.first_name && userProfile?.last_name
    ? `${userProfile.first_name.charAt(0)}${userProfile.last_name.charAt(0)}`
    : userProfile?.first_name?.charAt(0) || 'U';

  return (
    <header className="sticky top-0 z-40 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex h-16 items-center gap-4 px-4 lg:px-6">
        {/* Left Side - Action Buttons */}
        <div className="flex items-center gap-2">
          {/* Mobile Menu Button */}
          {showMenuButton && (
            <Button
              variant="ghost"
              size="icon"
              className="lg:hidden"
              onClick={onMenuClick}
            >
              <Menu className="h-5 w-5" />
              <span className="sr-only">פתח תפריט</span>
            </Button>
          )}

          {/* Quick Actions */}
          <div className="hidden md:flex items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="gap-2 cosmic-glow">
                  <Plus className="h-4 w-4" />
                  יצירה
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" className="w-48">
                <DropdownMenuLabel>מסמך חדש</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <FileText className="ml-2 h-4 w-4" />
                  חשבונית מס
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <FileText className="ml-2 h-4 w-4" />
                  חשבונית מס-קבלה
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Receipt className="ml-2 h-4 w-4" />
                  קבלה
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <FileText className="ml-2 h-4 w-4" />
                  הצעת מחיר
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <Receipt className="ml-2 h-4 w-4" />
                  הוצאה חדשה
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Theme Toggle */}
          <div className="flex items-center gap-2">
            <Moon size={16} className={cn(
              "transition-colors",
              isDarkMode ? 'text-foreground' : 'text-muted-foreground'
            )} />
            <Switch
              checked={isLightMode}
              onCheckedChange={toggleTheme}
              className="data-[state=checked]:bg-foreground"
              disabled={!mounted}
            />
            <Sun size={16} className={cn(
              "transition-colors",
              isLightMode ? 'text-foreground' : 'text-muted-foreground'
            )} />
          </div>

          {/* Notifications */}
          <Button variant="ghost" size="icon" className="relative cosmic-glow">
            <Bell className="h-5 w-5" />
            <Badge
              variant="destructive"
              className="absolute -top-1 -left-1 h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center bg-foreground text-background hover:bg-foreground/80"
            >
              3
            </Badge>
            <span className="sr-only">התראות</span>
          </Button>
        </div>

        {/* Center - Page Title */}
        {title && (
          <div className="flex-1 text-center">
            <h1 className="text-lg font-semibold text-foreground">{title}</h1>
            {subtitle && (
              <p className="text-sm text-muted-foreground">{subtitle}</p>
            )}
          </div>
        )}

        {/* Right Side - Search and User */}
        <div className="flex items-center gap-4">
          {/* Search Bar */}
          <div className="hidden md:block max-w-md">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                type="search"
                placeholder="חיפוש מסמכים, לקוחות..."
                className="pr-10 cosmic-glow"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-8 w-8 rounded-full cosmic-glow">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={userProfile?.avatar_url || ''} alt={userProfile?.first_name || ''} />
                  <AvatarFallback className="text-xs bg-muted text-foreground">
                    {userInitials}
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {userProfile?.first_name} {userProfile?.last_name}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {userProfile?.email}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <User className="ml-2 h-4 w-4" />
                <span>פרופיל</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Settings className="ml-2 h-4 w-4" />
                <span>הגדרות</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleSignOut}>
                <LogOut className="ml-2 h-4 w-4" />
                <span>התנתקות</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
};

export default AppHeader;
