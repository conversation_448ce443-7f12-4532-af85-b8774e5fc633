import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Separator } from '@/components/ui/separator';
import AppShell from '@/components/layout/AppShell';
import PageHeader from '@/components/layout/PageHeader';
import {
  FileText,
  Receipt,
  Plus,
  Trash2,
  Calculator,
  Save,
  Eye,
  Send,
  User,
  Building2,
  Calendar,
  DollarSign
} from 'lucide-react';

// Document type configurations
const documentTypes = {
  'tax-invoice': {
    label: 'חשבונית מס',
    code: '305',
    icon: FileText,
    description: 'חשבונית מס לפי חוק ישראלי',
    color: 'bg-blue-500',
  },
  'invoice-receipt': {
    label: 'חשבונית מס-קבלה',
    code: '320',
    icon: Receipt,
    description: 'חשבונית מס וקבלה במסמך אחד',
    color: 'bg-green-500',
  },
  'receipt': {
    label: 'קבלה',
    code: '400',
    icon: Receipt,
    description: 'קבלה על תשלום',
    color: 'bg-purple-500',
  },
  'quote': {
    label: 'הצעת מחיר',
    code: '200',
    icon: FileText,
    description: 'הצעת מחיר ללקוח',
    color: 'bg-orange-500',
  },
};

// Form validation schema
const lineItemSchema = z.object({
  description: z.string().min(1, 'תיאור נדרש'),
  quantity: z.number().min(0.01, 'כמות חייבת להיות גדולה מ-0'),
  unitPrice: z.number().min(0, 'מחיר יחידה לא יכול להיות שלילי'),
  vatRate: z.number().min(0).max(100, 'שיעור מע"מ חייב להיות בין 0-100'),
  total: z.number(),
});

const documentSchema = z.object({
  type: z.string(),
  clientName: z.string().min(1, 'שם לקוח נדרש'),
  clientEmail: z.string().email('כתובת אימייל לא תקינה').optional().or(z.literal('')),
  clientPhone: z.string().optional(),
  clientAddress: z.string().optional(),
  clientTaxId: z.string().optional(),
  issueDate: z.string().min(1, 'תאריך הנפקה נדרש'),
  dueDate: z.string().optional(),
  paymentTerms: z.string().optional(),
  notes: z.string().optional(),
  lineItems: z.array(lineItemSchema).min(1, 'נדרש לפחות פריט אחד'),
  subtotal: z.number(),
  vatAmount: z.number(),
  total: z.number(),
});

type DocumentFormData = z.infer<typeof documentSchema>;

const CreateDocument: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [documentNumber, setDocumentNumber] = useState('');
  
  const documentType = searchParams.get('type') || 'tax-invoice';
  const typeConfig = documentTypes[documentType as keyof typeof documentTypes];

  const form = useForm<DocumentFormData>({
    resolver: zodResolver(documentSchema),
    defaultValues: {
      type: documentType,
      clientName: '',
      clientEmail: '',
      clientPhone: '',
      clientAddress: '',
      clientTaxId: '',
      issueDate: new Date().toISOString().split('T')[0],
      dueDate: '',
      paymentTerms: '30',
      notes: '',
      lineItems: [
        {
          description: '',
          quantity: 1,
          unitPrice: 0,
          vatRate: 17,
          total: 0,
        },
      ],
      subtotal: 0,
      vatAmount: 0,
      total: 0,
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'lineItems',
  });

  // Generate document number on component mount
  useEffect(() => {
    const generateDocumentNumber = () => {
      const year = new Date().getFullYear();
      const typePrefix = typeConfig.code;
      const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
      return `${typePrefix}-${year}-${randomNum}`;
    };
    
    setDocumentNumber(generateDocumentNumber());
  }, [typeConfig.code]);

  // Calculate totals when line items change
  const watchedLineItems = form.watch('lineItems');
  
  useEffect(() => {
    let subtotal = 0;
    let vatAmount = 0;

    watchedLineItems.forEach((item, index) => {
      const itemTotal = item.quantity * item.unitPrice;
      const itemVat = (itemTotal * item.vatRate) / 100;
      
      subtotal += itemTotal;
      vatAmount += itemVat;
      
      // Update the total for this line item
      form.setValue(`lineItems.${index}.total`, itemTotal + itemVat);
    });

    form.setValue('subtotal', subtotal);
    form.setValue('vatAmount', vatAmount);
    form.setValue('total', subtotal + vatAmount);
  }, [watchedLineItems, form]);

  const addLineItem = () => {
    append({
      description: '',
      quantity: 1,
      unitPrice: 0,
      vatRate: 17,
      total: 0,
    });
  };

  const removeLineItem = (index: number) => {
    if (fields.length > 1) {
      remove(index);
    }
  };

  const onSubmit = (data: DocumentFormData) => {
    console.log('Document data:', data);
    // Here you would typically save the document to your backend
    // For now, we'll just navigate back to the documents list
    navigate('/app/documents');
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('he-IL', {
      style: 'currency',
      currency: 'ILS',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  return (
    <AppShell>
      <div className="space-y-6">
        <PageHeader
          title={`${typeConfig.label} חדש`}
          subtitle={typeConfig.description}
          breadcrumbs={[
            { label: 'לוח בקרה', href: '/dashboard' },
            { label: 'מסמכים', href: '/app/documents' },
            { label: typeConfig.label }
          ]}
          badge={{
            text: `מספר: ${documentNumber}`,
            variant: 'outline'
          }}
          actions={
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                onClick={() => setIsPreviewMode(!isPreviewMode)}
                className="gap-2"
              >
                <Eye className="h-4 w-4" />
                {isPreviewMode ? 'עריכה' : 'תצוגה מקדימה'}
              </Button>
              <Button
                variant="outline"
                onClick={() => form.handleSubmit(onSubmit)()}
                className="gap-2"
              >
                <Save className="h-4 w-4" />
                שמירה כטיוטה
              </Button>
              <Button
                onClick={() => form.handleSubmit(onSubmit)()}
                className="gap-2"
              >
                <Send className="h-4 w-4" />
                שמירה ושליחה
              </Button>
            </div>
          }
        />

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid gap-6 lg:grid-cols-3">
              {/* Client Information */}
              <div className="lg:col-span-2">
                <Card className="cosmic-card">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <User className="h-5 w-5" />
                      פרטי לקוח
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="clientName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>שם לקוח *</FormLabel>
                            <FormControl>
                              <Input placeholder="שם החברה או הלקוח" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="clientTaxId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>מספר עוסק מורשה</FormLabel>
                            <FormControl>
                              <Input placeholder="*********" {...field} />
                            </FormControl>
                            <FormDescription>
                              נדרש לחשבוניות מס
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid gap-4 md:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="clientEmail"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>אימייל</FormLabel>
                            <FormControl>
                              <Input type="email" placeholder="<EMAIL>" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="clientPhone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>טלפון</FormLabel>
                            <FormControl>
                              <Input placeholder="050-1234567" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="clientAddress"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>כתובת</FormLabel>
                          <FormControl>
                            <Textarea 
                              placeholder="כתובת מלאה של הלקוח"
                              className="min-h-[80px]"
                              {...field} 
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </div>

              {/* Document Details */}
              <div>
                <Card className="cosmic-card">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Calendar className="h-5 w-5" />
                      פרטי מסמך
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center gap-3 p-3 rounded-lg bg-accent/20">
                      <div className={`w-8 h-8 rounded-lg ${typeConfig.color} flex items-center justify-center`}>
                        <typeConfig.icon className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <div className="font-medium">{typeConfig.label}</div>
                        <div className="text-sm text-muted-foreground">קוד: {typeConfig.code}</div>
                      </div>
                    </div>

                    <FormField
                      control={form.control}
                      name="issueDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>תאריך הנפקה *</FormLabel>
                          <FormControl>
                            <Input type="date" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {(documentType === 'tax-invoice' || documentType === 'quote') && (
                      <FormField
                        control={form.control}
                        name="dueDate"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>תאריך יעד לתשלום</FormLabel>
                            <FormControl>
                              <Input type="date" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}

                    <FormField
                      control={form.control}
                      name="paymentTerms"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>תנאי תשלום</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="בחר תנאי תשלום" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="0">מיידי</SelectItem>
                              <SelectItem value="15">15 יום</SelectItem>
                              <SelectItem value="30">30 יום</SelectItem>
                              <SelectItem value="45">45 יום</SelectItem>
                              <SelectItem value="60">60 יום</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Line Items */}
            <Card className="cosmic-card">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    פריטים
                  </CardTitle>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addLineItem}
                    className="gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    הוסף פריט
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {fields.map((field, index) => (
                    <div key={field.id} className="grid gap-4 p-4 border rounded-lg">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">פריט {index + 1}</h4>
                        {fields.length > 1 && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeLineItem(index)}
                            className="text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>

                      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
                        <div className="lg:col-span-2">
                          <FormField
                            control={form.control}
                            name={`lineItems.${index}.description`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>תיאור *</FormLabel>
                                <FormControl>
                                  <Input placeholder="תיאור השירות או המוצר" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <FormField
                          control={form.control}
                          name={`lineItems.${index}.quantity`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>כמות</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  step="0.01"
                                  min="0"
                                  {...field}
                                  onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name={`lineItems.${index}.unitPrice`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>מחיר יחידה</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  step="0.01"
                                  min="0"
                                  {...field}
                                  onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name={`lineItems.${index}.vatRate`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>מע"מ (%)</FormLabel>
                              <Select
                                onValueChange={(value) => field.onChange(parseFloat(value))}
                                defaultValue={field.value.toString()}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="0">0% (פטור)</SelectItem>
                                  <SelectItem value="17">17% (רגיל)</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="flex justify-end">
                        <div className="text-sm text-muted-foreground">
                          סה"כ פריט: {formatCurrency(form.watch(`lineItems.${index}.total`) || 0)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Totals */}
            <Card className="cosmic-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calculator className="h-5 w-5" />
                  סיכום
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <FormField
                        control={form.control}
                        name="notes"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>הערות</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="הערות נוספות למסמך"
                                className="min-h-[100px]"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="space-y-3">
                      <div className="flex justify-between items-center py-2">
                        <span className="text-muted-foreground">סכום ביניים:</span>
                        <span className="font-medium">
                          {formatCurrency(form.watch('subtotal') || 0)}
                        </span>
                      </div>

                      <div className="flex justify-between items-center py-2">
                        <span className="text-muted-foreground">מע"מ:</span>
                        <span className="font-medium">
                          {formatCurrency(form.watch('vatAmount') || 0)}
                        </span>
                      </div>

                      <Separator />

                      <div className="flex justify-between items-center py-2">
                        <span className="text-lg font-semibold">סה"כ לתשלום:</span>
                        <span className="text-lg font-bold">
                          {formatCurrency(form.watch('total') || 0)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <div className="flex justify-end gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate('/app/documents')}
              >
                ביטול
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => form.handleSubmit(onSubmit)()}
              >
                <Save className="ml-2 h-4 w-4" />
                שמירה כטיוטה
              </Button>
              <Button type="submit">
                <Send className="ml-2 h-4 w-4" />
                שמירה ושליחה
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </AppShell>
  );
};

export default CreateDocument;
